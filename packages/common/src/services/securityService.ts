import { AxiosResponse } from "axios";
import { BehaviorSubject } from "rxjs";
import { HttpService } from "./httpService";
import { AuthResponse, User } from "./types";

export const $userPermissions = new BehaviorSubject<string[]>([]);

const baseUrl = process.env.STANDALONE === 'false' ?window.location.href
    .substring(0, window.location.href.indexOf("react") - 1)
    .replace(window.location.origin, "")
  ? `${window.location.href
      .substring(0, window.location.href.indexOf("react") - 1)
      .replace(window.location.origin, "")}/rest/permissions`
  : "/pmi/rest/permissions" : `${process.env.PMI_URL ? process.env.PMI_URL.split('"').join('') : 'http://localhost:8080/pmi'}/rest/permissions`;

console.log("Security service baseUrl", baseUrl);

export const SecurityService = {
  init(): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      this.updateUserPermissions()
        .then(() => {
          resolve();
        })
        .catch(reject);
    });
  },

  updateUserPermissions(): Promise<void> {
    // Skip HTTP call when in standalone mode
    if (process.env.STANDALONE !== 'false') {
      console.log("SecurityService: Skipping permission check in standalone mode");
      // Set some default permissions if needed
      $userPermissions.next(['ADMIN', 'SMSC_CONNECTION_UPDATE_PERMISSION', 'SMSC_APPLICATION_UPDATE_PERMISSION']);
      return Promise.resolve();
    }

    return new Promise<void>((resolve) => {
      HttpService.getWithoutRouting(baseUrl)
        .then((response: AxiosResponse<string[]>) => {
          const ps = [...response.data];
          // console.log("ps", ps);
          $userPermissions.next(ps);
          resolve();
        })
        .catch((err) => {
          console.error("Error fetching permissions:", err);
          // Set default permissions even on error
          $userPermissions.next(['ADMIN', 'SMSC_CONNECTION_UPDATE_PERMISSION', 'SMSC_APPLICATION_UPDATE_PERMISSION']);
          resolve(); // Resolve instead of reject to prevent app from failing
        });
    });
  },

  checkPermission(_permission: string): boolean {
    // TODO: remove this
    return true;
    // return (
    //   $userPermissions.getValue() &&
    //   $userPermissions.getValue().indexOf(permission) !== -1
    // );
  },

  checkPermissionsAnd(_permissions: string[]): boolean {
    // TODO: remove this
    return true;
    // return (
    //   $userPermissions.getValue() &&
    //   !permissions.some((p) => $userPermissions.getValue().indexOf(p) === -1)
    // );
  },

  getUserPermissions(): Promise<string[]> {
    // Returns the current permissions as a Promise
    return Promise.resolve($userPermissions.getValue());
  },

  checkPermissionsOr(_permissions: string[]): boolean {
    // TODO: remove this
    return true;
    // return (
    //   $userPermissions.getValue() &&
    //   permissions.some((p) => $userPermissions.getValue().indexOf(p) !== -1)
    // );
  },

  // Additional methods for authentication
  login(username: string, _password: string): Promise<AuthResponse> {
    // This is a placeholder - implement actual login logic
    return Promise.resolve({ token: "sample-token", user: { username } });
  },

  logout(): Promise<void> {
    // This is a placeholder - implement actual logout logic
    return Promise.resolve();
  },

  getCurrentUser(): Promise<User | null> {
    // This is a placeholder - implement actual user retrieval
    return Promise.resolve(null);
  }
};
