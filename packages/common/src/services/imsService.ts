import { HttpService } from "./httpService";
import { $properties } from "./propertiesService";
import { SecurityService } from "./securityService";
import { User, ImsMessage, HttpResponse } from "./types";

interface Tenant {
  id?: string | number;
  name?: string;
  description?: string;
  [key: string]: any;
}

interface Role {
  id?: string | number;
  name?: string;
  description?: string;
  permissions?: string[];
  [key: string]: any;
}

interface Permission {
  id?: string | number;
  name?: string;
  description?: string;
  [key: string]: any;
}

interface PasswordRules {
  minPasswordLength: number;
  maxPasswordLength: number;
  minUpperCase: number;
  minLowerCase: number;
  minDigits: number;
  minCharacteristics: number;
  lowercaseRestriction: boolean;
  uppercaseRestriction: boolean;
  specialCharacterRule: boolean;
  adminChangePassword: boolean;
}

// Helper function to get the IMS base URL
const getImsBaseUrl = (): string => {
  return (
    $properties.value["client.ims.base.url"] ||
    $properties.value["ims.client.primary.service.url"] ||
    $properties.value["client.ims.client.primary.service.url"] ||
    "http://ims:8225/"
  );
};

export const IMSService = {
  getTenants(): Promise<Tenant[]> {
    return new Promise<Tenant[]>((resolve, reject) => {
      if (SecurityService.checkPermission("IMS_TENANT_READ_PERMISSION")) {
        HttpService.get<Tenant[]>(
          `${getImsBaseUrl()}/identity/tenants`
        )
          .then((response: HttpResponse<Tenant[]>) => {
            if (response.status >= 200 && response.status < 300) {
              resolve(response.data);
            } else {
              reject(response.statusText);
            }
          })
          .catch(reject);
      } else {
        console.error("IMS_TENANT_READ_PERMISSION is required.");
        resolve([]);
      }
    });
  },

  getTenantById(id: string | number): Promise<Tenant | null> {
    return new Promise<Tenant | null>((resolve, reject) => {
      if (SecurityService.checkPermission("IMS_TENANT_READ_PERMISSION")) {
        HttpService.get<Tenant>(
          `${getImsBaseUrl()}/identity/tenants/${id}`
        )
          .then((response: HttpResponse<Tenant>) => {
            if (response.status >= 200 && response.status < 300) {
              resolve(response.data);
            } else {
              reject(response.statusText);
            }
          })
          .catch(reject);
      } else {
        console.error("IMS_TENANT_READ_PERMISSION is required.");
        resolve(null);
      }
    });
  },

  deleteTenantById(id: string | number): Promise<any> {
    return new Promise<any>((resolve, reject) => {
      if (SecurityService.checkPermission("IMS_TENANT_DELETE_PERMISSION")) {
        HttpService.delete(
          `${getImsBaseUrl()}/identity/tenants/${id}`
        )
          .then((response: HttpResponse<any>) => {
            if (response.status >= 200 && response.status < 300) {
              resolve(response.data);
            } else {
              reject(response.statusText);
            }
          })
          .catch(reject);
      } else {
        console.error("IMS_TENANT_DELETE_PERMISSION is required.");
        resolve(null);
      }
    });
  },

  saveTenant(tenant: Tenant): Promise<Tenant | null> {
    return new Promise<Tenant | null>((resolve, reject) => {
      if (
        tenant.id
          ? SecurityService.checkPermission("IMS_ROLE_UPDATE_PERMISSION")
          : SecurityService.checkPermission("IMS_ROLE_CREATE_PERMISSION")
      ) {
        HttpService[tenant.id ? "put" : "post"]<Tenant>(
          `${getImsBaseUrl()}/identity/tenants${tenant.id ? `/${tenant.id}` : ""}`,
          {},
          tenant
        )
          .then((response: HttpResponse<Tenant>) => {
            if (response.status >= 200 && response.status < 300) {
              resolve(response.data);
            } else {
              reject(response.statusText);
            }
          })
          .catch(reject);
      } else {
        console.error(
          tenant.id
            ? "IMS_ROLE_UPDATE_PERMISSION is required."
            : "IMS_ROLE_CREATE_PERMISSION is required."
        );
        resolve(null);
      }
    });
  },

  getPermissions(): Promise<Permission[]> {
    return new Promise<Permission[]>((resolve, reject) => {
      if (SecurityService.checkPermission("IMS_ROLE_READ_PERMISSION")) {
        HttpService.get<Permission[]>(
          `${getImsBaseUrl()}/identity/permissions`
        )
          .then((response: HttpResponse<Permission[]>) => {
            if (response.status >= 200 && response.status < 300) {
              resolve(response.data);
            } else {
              reject(response.statusText);
            }
          })
          .catch(reject);
      } else {
        console.error("IMS_ROLE_READ_PERMISSION is required.");
        resolve([]);
      }
    });
  },

  getRoles(): Promise<Role[]> {
    return new Promise<Role[]>((resolve, reject) => {
      if (SecurityService.checkPermission("IMS_ROLE_READ_PERMISSION")) {
        HttpService.get<Role[]>(
          `${getImsBaseUrl()}/identity/role`
        )
          .then((response: HttpResponse<Role[]>) => {
            if (response.status >= 200 && response.status < 300) {
              resolve(response.data);
            } else {
              reject(response.statusText);
            }
          })
          .catch(reject);
      } else {
        console.error("IMS_ROLE_READ_PERMISSION is required.");
        resolve([]);
      }
    });
  },

  getRoleById(id: string | number): Promise<Role | null> {
    return new Promise<Role | null>((resolve, reject) => {
      if (SecurityService.checkPermission("IMS_ROLE_READ_PERMISSION")) {
        HttpService.get<Role>(
          `${getImsBaseUrl()}/identity/role/${id}`
        )
          .then((response: HttpResponse<Role>) => {
            if (response.status >= 200 && response.status < 300) {
              resolve(response.data);
            } else {
              reject(response.statusText);
            }
          })
          .catch(reject);
      } else {
        console.error("IMS_ROLE_READ_PERMISSION is required.");
        resolve(null);
      }
    });
  },

  deleteRoleById(id: string | number): Promise<any> {
    return new Promise<any>((resolve, reject) => {
      if (SecurityService.checkPermission("IMS_ROLE_DELETE_PERMISSION")) {
        HttpService.delete(
          `${getImsBaseUrl()}/identity/role/${id}`
        )
          .then((response: HttpResponse<any>) => {
            if (response.status >= 200 && response.status < 300) {
              resolve(response.data);
            } else {
              reject(response.statusText);
            }
          })
          .catch(reject);
      } else {
        console.error("IMS_ROLE_DELETE_PERMISSION is required.");
        resolve(null);
      }
    });
  },

  saveRole(role: Role): Promise<Role | null> {
    return new Promise<Role | null>((resolve, reject) => {
      if (
        role.id
          ? SecurityService.checkPermission("IMS_ROLE_UPDATE_PERMISSION")
          : SecurityService.checkPermission("IMS_ROLE_CREATE_PERMISSION")
      ) {
        HttpService.post<Role>(
          `${getImsBaseUrl()}/identity/role`,
          {},
          role
        )
          .then((response: HttpResponse<Role>) => {
            if (response.status >= 200 && response.status < 300) {
              resolve(response.data);
            } else {
              reject(response.statusText);
            }
          })
          .catch(reject);
      } else {
        console.error(
          role.id
            ? "IMS_ROLE_UPDATE_PERMISSION is required."
            : "IMS_ROLE_CREATE_PERMISSION is required."
        );
        resolve(null);
      }
    });
  },

  getUsers(): Promise<User[]> {
    return new Promise<User[]>((resolve, reject) => {
      if (SecurityService.checkPermission("IMS_USER_READ_PERMISSION")) {
        HttpService.get<User[]>(
          `${getImsBaseUrl()}/identity/user`
        )
          .then((response: HttpResponse<User[]>) => {
            if (response.status >= 200 && response.status < 300) {
              resolve(response.data);
            } else {
              reject(response.statusText);
            }
          })
          .catch(reject);
      } else {
        console.error("IMS_USER_READ_PERMISSION is required.");
        resolve([]);
      }
    });
  },

  getUserById(id: string | number): Promise<User | null> {
    return new Promise<User | null>((resolve, reject) => {
      if (SecurityService.checkPermission("IMS_USER_READ_PERMISSION")) {
        HttpService.get<User>(
          `${getImsBaseUrl()}/identity/user/${id}`
        )
          .then((response: HttpResponse<User>) => {
            if (response.status >= 200 && response.status < 300) {
              resolve(response.data);
            } else {
              reject(response.statusText);
            }
          })
          .catch(reject);
      } else {
        console.error("IMS_USER_READ_PERMISSION is required.");
        resolve(null);
      }
    });
  },

  deleteUserById(id: string | number): Promise<any> {
    return new Promise<any>((resolve, reject) => {
      if (SecurityService.checkPermission("IMS_USER_DELETE_PERMISSION")) {
        HttpService.delete(
          `${getImsBaseUrl()}/identity/user/${id}`
        )
          .then((response: HttpResponse<any>) => {
            if (response.status >= 200 && response.status < 300) {
              resolve(response.data);
            } else {
              reject(response.statusText);
            }
          })
          .catch(reject);
      } else {
        console.error("IMS_USER_DELETE_PERMISSION is required.");
        resolve(null);
      }
    });
  },

  saveUser(user: User): Promise<User | null> {
    return new Promise<User | null>((resolve, reject) => {
      if (
        user.id
          ? SecurityService.checkPermission("IMS_USER_UPDATE_PERMISSION")
          : SecurityService.checkPermission("IMS_USER_CREATE_PERMISSION")
      ) {
        HttpService[user.id ? "put" : "post"]<User>(
          `${getImsBaseUrl()}/identity/user${user.id ? `/${user.id}` : ""}`,
          {},
          user
        )
          .then((response: HttpResponse<User>) => {
            if (response.status >= 200 && response.status < 300) {
              resolve(response.data);
            } else {
              reject(response.statusText);
            }
          })
          .catch(reject);
      } else {
        console.error(
          user.id
            ? "IMS_USER_UPDATE_PERMISSION is required."
            : "IMS_USER_CREATE_PERMISSION is required."
        );
        resolve(null);
      }
    });
  },

  getPasswordRules(): Promise<PasswordRules> {
    return new Promise<PasswordRules>((resolve, reject) => {
      if (SecurityService.checkPermission("IMS_USER_READ_PERMISSION")) {
        HttpService.get<Record<string, any>>(
          `${getImsBaseUrl()}/identity/browser/properties/domain.security.password.enable_lowercase_restriction,domain.security.password.enable_uppercase_restriction,domain.security.password.special_character_rule,domain.security.password.max_password_length,domain.security.password.min_upper_case,domain.security.password.min_lower_case,domain.security.allow.admin.to.change.passwords,domain.security.password.min_digits,domain.security.password.min_characteristics`
        )
          .then((response: HttpResponse<Record<string, any>>) => {
            if (response.status >= 200 && response.status < 300) {
              const minPasswordLengthDefault = 8;
              const maxPasswordLengthDefault = 50;
              const minUpperCaseDefault = 1;
              const minLowerCaseDefault = 1;
              const minDigitsDefault = 0;
              const minCharacteristicsDefault = 0;
              const lowercaseRestrictionDefault = true;
              const uppercaseRestrictionDefault = true;
              const specialCharacterRuleDefault = true;
              const adminChangePasswordDefault = true;

              const { data } = response;
              console.log("DATA", data);

              resolve({
                minPasswordLength: /[0-9]/.test(
                  data["domain.security.password.min_password_length"]
                )
                  ? parseInt(data["domain.security.password.min_password_length"], 10)
                  : minPasswordLengthDefault,
                maxPasswordLength: /[0-9]/.test(
                  data["domain.security.password.max_password_length"]
                )
                  ? parseInt(data["domain.security.password.max_password_length"], 10)
                  : maxPasswordLengthDefault,
                minUpperCase: /[0-9]/.test(
                  data["domain.security.password.min_upper_case"]
                )
                  ? parseInt(data["domain.security.password.min_upper_case"], 10)
                  : minUpperCaseDefault,
                minLowerCase: /[0-9]/.test(
                  data["domain.security.password.min_lower_case"]
                )
                  ? parseInt(data["domain.security.password.min_lower_case"], 10)
                  : minLowerCaseDefault,
                minDigits: /[0-9]/.test(
                  data["domain.security.password.min_digits"]
                )
                  ? parseInt(data["domain.security.password.min_digits"], 10)
                  : minDigitsDefault,
                minCharacteristics: /[0-9]/.test(
                  data["domain.security.password.min_characteristics"]
                )
                  ? parseInt(data["domain.security.password.min_characteristics"], 10)
                  : minCharacteristicsDefault,
                lowercaseRestriction:
                  data[
                    "domain.security.password.enable_lowercase_restriction"
                  ] === true ||
                  data[
                    "domain.security.password.enable_lowercase_restriction"
                  ] === "true"
                    ? true
                    : lowercaseRestrictionDefault,
                uppercaseRestriction:
                  data[
                    "domain.security.password.enable_uppercase_restriction"
                  ] === true ||
                  data[
                    "domain.security.password.enable_uppercase_restriction"
                  ] === "true"
                    ? true
                    : uppercaseRestrictionDefault,
                specialCharacterRule:
                  data["domain.security.password.special_character_rule"] ===
                    true ||
                  data["domain.security.password.special_character_rule"] ===
                    "true"
                    ? true
                    : specialCharacterRuleDefault,
                adminChangePassword:
                  data["domain.security.allow.admin.to.change.passwords"] ===
                    true ||
                  data["domain.security.allow.admin.to.change.passwords"] ===
                    "true"
                    ? true
                    : adminChangePasswordDefault,
              });
            } else {
              reject(response.statusText);
            }
          })
          .catch(reject);
      } else {
        console.error("IMS_USER_READ_PERMISSION is required.");
        resolve({
          minPasswordLength: 8,
          maxPasswordLength: 50,
          minUpperCase: 1,
          minLowerCase: 1,
          minDigits: 0,
          minCharacteristics: 0,
          lowercaseRestriction: true,
          uppercaseRestriction: true,
          specialCharacterRule: true,
          adminChangePassword: true
        });
      }
    });
  },
  
  // Additional methods for IMS messaging
  sendMessage(message: ImsMessage): Promise<ImsMessage> {
    return new Promise<ImsMessage>((resolve, reject) => {
      HttpService.post<ImsMessage>(
        `${getImsBaseUrl()}/messaging/send`,
        {},
        message
      )
        .then((response: HttpResponse<ImsMessage>) => {
          if (response.status >= 200 && response.status < 300) {
            resolve(response.data);
          } else {
            reject(response.statusText);
          }
        })
        .catch(reject);
    });
  },
  
  getMessageStatus(messageId: string): Promise<ImsMessage> {
    return new Promise<ImsMessage>((resolve, reject) => {
      HttpService.get<ImsMessage>(
        `${getImsBaseUrl()}/messaging/status/${messageId}`
      )
        .then((response: HttpResponse<ImsMessage>) => {
          if (response.status >= 200 && response.status < 300) {
            resolve(response.data);
          } else {
            reject(response.statusText);
          }
        })
        .catch(reject);
    });
  }
};
