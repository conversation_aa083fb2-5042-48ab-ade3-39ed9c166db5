import { LogEntry, LogLevel } from './types';

function init(): void {
  // Raven.config("https://<EMAIL>/1392445", {
  //  release: "0-0-1",
  //  environment: "development-test"
  // }).install();
}

function log(error: Error | string | unknown): void {
  console.error(error);
  // Raven.captureException("Logging the error", error);
}

function debug(message: string, data?: any): void {
  const logEntry: LogEntry = {
    level: LogLevel.DEBUG,
    message,
    timestamp: new Date(),
    data
  };
  console.debug(logEntry.message, logEntry.data);
}

function info(message: string, data?: any): void {
  const logEntry: LogEntry = {
    level: LogLevel.INFO,
    message,
    timestamp: new Date(),
    data
  };
  console.info(logEntry.message, logEntry.data);
}

function warn(message: string, data?: any): void {
  const logEntry: LogEntry = {
    level: LogLevel.WARN,
    message,
    timestamp: new Date(),
    data
  };
  console.warn(logEntry.message, logEntry.data);
}

function error(message: string, data?: any): void {
  const logEntry: LogEntry = {
    level: LogLevel.ERROR,
    message,
    timestamp: new Date(),
    data
  };
  console.error(logEntry.message, logEntry.data);
}

export default {
  init,
  log,
  debug,
  info,
  warn,
  error
};
