import { RoutingClass } from "@pnmui/common/types/smscTypes";
export declare const SmscRoutingClassesService: {
    getRoutingClasses(): Promise<RoutingClass[]>;
    saveRoutingClass(routingClass: RoutingClass): Promise<RoutingClass | []>;
    deleteRoutingClassById(id: string | number): Promise<boolean | []>;
    saveRoutingClassWithName(routingClassName: string): Promise<RoutingClass>;
    getRoutingClassById(id: string | number): Promise<RoutingClass>;
};
