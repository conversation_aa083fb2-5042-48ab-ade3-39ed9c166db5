import { HttpService } from "@pnmui/common/services/httpService";
import { $properties } from "@pnmui/common/services/propertiesService";
import { NumberList } from "@pnmui/common/types/smscTypes";

export const SmscNumberListsService = {
  getNumberLists(): Promise<NumberList[]> {
    return new Promise((resolve, reject) => {
      HttpService.get<NumberList[]>(
        `${
          $properties.value["client.smsws.base.url"] || "http://localhost:3500"
        }/numberLists`,
        {}
      )
        .then((response) => {
          if (response.status === 200 || response.status === 201) {
            resolve(response.data);
          } else {
            reject(response.statusText);
          }
        })
        .catch(reject);
    });
  },
  
  getNumberListById(id: string | number): Promise<NumberList> {
    return new Promise((resolve, reject) => {
      HttpService.get<NumberList>(
        `${
          $properties.value["client.smsws.base.url"] || "http://localhost:3500"
        }/numberLists/${id}`,
        {}
      )
        .then((response) => {
          if (response.status === 200 || response.status === 201) {
            resolve(response.data);
          } else {
            reject(response.statusText);
          }
        })
        .catch(reject);
    });
  },
  
  saveNumberList(numberList: NumberList): Promise<NumberList> {
    return new Promise((resolve, reject) => {
      HttpService[numberList.id ? "put" : "post"]<NumberList>(
        `${
          $properties.value["client.smsws.base.url"] || "http://localhost:3500"
        }/numberLists${numberList.id ? "/" + numberList.id : ""}`,
        {},
        numberList
      )
        .then((response) => {
          if (response.status === 200 || response.status === 201) {
            resolve(response.data);
          } else {
            reject(response.statusText);
          }
        })
        .catch(reject);
    });
  },
  
  saveNumberListWithName(numberList: { name: string }): Promise<NumberList> {
    return new Promise((resolve, reject) => {
      HttpService.post<NumberList>(
        `${
          $properties.value["client.smsws.base.url"] || "http://localhost:3500"
        }/numberLists/createWithName`,
        {},
        numberList
      )
        .then((response) => {
          if (response.status === 200 || response.status === 201) {
            resolve(response.data);
          } else {
            reject(response.statusText);
          }
        })
        .catch(reject);
    });
  },
  
  deleteNumberListById(id: string | number): Promise<boolean> {
    return new Promise((resolve, reject) => {
      HttpService.delete(
        `${
          $properties.value["client.smsws.base.url"] || "http://localhost:3500"
        }/numberLists/${id}`,
        {}
      )
        .then((response) => {
          if (response.status === 200 || response.status === 201) {
            resolve(response.data);
          } else {
            reject(response.statusText);
          }
        })
        .catch(reject);
    });
  },
};


