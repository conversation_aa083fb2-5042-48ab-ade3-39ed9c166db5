import { Connection } from "@pnmui/common/types/smscTypes";
export declare const SmscConnectionsService: {
    getConnections(): Promise<Connection[]>;
    getConnectionById(id: string | number): Promise<Connection>;
    saveConnection(connection: Connection): Promise<Connection>;
    saveConnectionWithName(connection: {
        name: string;
    }): Promise<Connection>;
    deleteConnectionById(id: string | number): Promise<boolean>;
    loadFromFileSystem(entityType: "connections" | "applications", host?: string): Promise<any>;
};
