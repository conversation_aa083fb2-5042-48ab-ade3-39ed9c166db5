import { HttpService } from "@pnmui/common/services/httpService";
import { $properties } from "@pnmui/common/services/propertiesService";
import { SecurityService } from "@pnmui/common/services/securityService";
import { RoutingClass, SMSRoutingTable } from "@pnmui/common/types/smscTypes";

// Base URL will be determined from properties

export const SmscSmsRoutingTablesService = {
  getSmsRoutingTables(): Promise<SMSRoutingTable[]> {
    return new Promise((resolve, reject) => {
      if (
        SecurityService.checkPermission("SMSC_ROUTING_TABLES_READ_PERMISSION")
      ) {
        HttpService.get<SMSRoutingTable[]>(
          `${
            $properties.value["client.smsws.base.url"] ||
            "http://localhost:3500"
          }/mORoutingTables`,
          {}
        )
          .then((response) => {
            if (response.status === 200 || response.status === 201) {
              resolve(response.data);
            } else {
              reject(response.statusText);
            }
          })
          .catch(reject);
      } else {
        console.error("SMSC_ROUTING_TABLES_READ_PERMISSION is required.");
        resolve([]);
      }
    });
  },

  getSmsRoutingTableById(id: string | number): Promise<SMSRoutingTable | null> {
    return new Promise((resolve, reject) => {
      if (
        SecurityService.checkPermission("SMSC_ROUTING_TABLES_READ_PERMISSION")
      ) {
        HttpService.get<SMSRoutingTable>(
          `${
            $properties.value["client.smsws.base.url"] ||
            "http://localhost:3500"
          }/mORoutingTables/${id}`,
          {}
        )
          .then((response) => {
            if (response.status === 200 || response.status === 201) {
              resolve(response.data);
            } else {
              reject(response.statusText);
            }
          })
          .catch(reject);
      } else {
        console.error("SMSC_ROUTING_TABLES_READ_PERMISSION is required.");
        resolve(null);
      }
    });
  },

  saveSmsRoutingTable(smsRoutingTable: SMSRoutingTable): Promise<SMSRoutingTable> {
    return new Promise((resolve, reject) => {
      const permission = smsRoutingTable.id
        ? "SMSC_ROUTING_TABLES_UPDATE_PERMISSION"
        : "SMSC_ROUTING_TABLES_CREATE_PERMISSION";

      if (SecurityService.checkPermission(permission)) {
        HttpService[smsRoutingTable.id ? "put" : "post"]<SMSRoutingTable>(
          `${
            $properties.value["client.smsws.base.url"] ||
            "http://localhost:3500"
          }/mORoutingTables${
            smsRoutingTable.id ? "/" + smsRoutingTable.id : ""
          }`,
          {},
          smsRoutingTable
        )
          .then((response) => {
            if (response.status === 200 || response.status === 201) {
              resolve(response.data);
            } else {
              reject(response.statusText);
            }
          })
          .catch(reject);
      } else {
        console.error(`${permission} is required.`);
        reject(
          new Error(
            `Unauthorized: Missing ${permission.split("_")[3]} permission`
          )
        );
      }
    });
  },

  deleteSmsRoutingTableById(id: string | number): Promise<boolean> {
    return new Promise((resolve, reject) => {
      if (
        SecurityService.checkPermission("SMSC_ROUTING_TABLES_DELETE_PERMISSION")
      ) {
        HttpService.delete(
          `${
            $properties.value["client.smsws.base.url"] ||
            "http://localhost:3500"
          }/mORoutingTables/${id}`,
          {}
        )
          .then((response) => {
            if (response.status === 200 || response.status === 201) {
              resolve(response.data);
            } else {
              reject(response.statusText);
            }
          })
          .catch(reject);
      } else {
        console.error("SMSC_ROUTING_TABLES_DELETE_PERMISSION is required.");
        reject(new Error("Unauthorized: Missing DELETE permission"));
      }
    });
  },

  saveRoutingClassWithName(routingClassName: string): Promise<RoutingClass> {
    return new Promise((resolve, reject) => {
      HttpService.post<RoutingClass>(
        `${
          $properties.value["client.smsws.base.url"] || "http://localhost:3500"
        }/mORoutingClasses/createWithName`,
        {},
        { name: routingClassName }
      )
        .then((response) => {
          if (response.status === 200 || response.status === 201) {
            resolve(response.data);
          } else {
            reject(response.statusText);
          }
        })
        .catch(reject);
    });
  },
};


