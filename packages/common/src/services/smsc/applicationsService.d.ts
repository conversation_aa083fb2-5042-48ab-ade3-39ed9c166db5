import { Application } from "@pnmui/common/types/smscTypes";
export declare const SmscApplicationsService: {
    getApplications(): Promise<Application[]>;
    getApplicationById(id: string | number): Promise<Application>;
    saveApplication(application: Application): Promise<Application | null>;
    createApplicationWithName(application: {
        name: string;
    }): Promise<Application>;
    deleteApplicationById(id: string | number): Promise<boolean | null>;
};
