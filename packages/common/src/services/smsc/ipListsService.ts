import { HttpService } from "@pnmui/common/services/httpService";
import { $properties } from "@pnmui/common/services/propertiesService";
import { SecurityService } from "@pnmui/common/services/securityService";
import { IPList } from "@pnmui/common/types/smscTypes";

export const SmscIpListsService = {
  createIpListWithName(ipList: { name: string }): Promise<IPList> {
    return new Promise((resolve, reject) => {
      HttpService.post<IPList>(
        `${
          $properties.value["client.smsws.base.url"] || "http://localhost:3500"
        }/ipLists/createWithName`,
        {},
        ipList
      )
        .then((response) => {
          if (response.status === 200 || response.status === 201) {
            resolve(response.data);
          } else {
            reject(response.statusText);
          }
        })
        .catch(reject);
    });
  },

  async getIpLists(): Promise<IPList[]> {
    if (!SecurityService.checkPermission("SMSC_IP_LISTS_READ_PERMISSION")) {
      return Promise.reject("Permission Denied: Read Access");
    }

    return new Promise((resolve, reject) => {
      HttpService.get<IPList[]>(
        `${
          $properties.value["client.smsws.base.url"] || "http://localhost:3500"
        }/ipLists`,
        {}
      )
        .then((response) => {
          if (response.status === 200 || response.status === 201) {
            resolve(response.data);
          } else {
            reject(response.statusText);
          }
        })
        .catch(reject);
    });
  },

  async getIpListById(id: string | number): Promise<IPList> {
    if (!SecurityService.checkPermission("SMSC_IP_LISTS_READ_PERMISSION")) {
      return Promise.reject("Permission Denied: Read Access");
    }

    return new Promise((resolve, reject) => {
      HttpService.get<IPList>(
        `${
          $properties.value["client.smsws.base.url"] || "http://localhost:3500"
        }/ipLists/${id}`,
        {}
      )
        .then((response) => {
          if (response.status === 200 || response.status === 201) {
            resolve(response.data);
          } else {
            reject(response.statusText);
          }
        })
        .catch(reject);
    });
  },

  async saveIpList(ipList: IPList): Promise<IPList> {
    if (!SecurityService.checkPermission("SMSC_IP_LISTS_UPDATE_PERMISSION")) {
      return Promise.reject("Permission Denied: Update Access");
    }

    return new Promise((resolve, reject) => {
      HttpService[ipList.id ? "put" : "post"]<IPList>(
        `${
          $properties.value["client.smsws.base.url"] || "http://localhost:3500"
        }/ipLists${ipList.id ? "/" + ipList.id : ""}`,
        {},
        ipList
      )
        .then((response) => {
          if (response.status === 200 || response.status === 201) {
            resolve(response.data);
          } else {
            reject(response.statusText);
          }
        })
        .catch(reject);
    });
  },

  async deleteIpListById(id: string | number): Promise<boolean> {
    if (!SecurityService.checkPermission("SMSC_IP_LISTS_DELETE_PERMISSION")) {
      return Promise.reject("Permission Denied: Delete Access");
    }

    return new Promise((resolve, reject) => {
      HttpService.delete(
        `${
          $properties.value["client.smsws.base.url"] || "http://localhost:3500"
        }/ipLists/${id}`,
        {}
      )
        .then((response) => {
          if (response.status === 200 || response.status === 201) {
            resolve(response.data);
          } else {
            reject(response.statusText);
          }
        })
        .catch(reject);
    });
  },
};

