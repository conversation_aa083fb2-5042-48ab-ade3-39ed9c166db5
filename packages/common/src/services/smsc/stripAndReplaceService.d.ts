import { StripAndReplace, StripAndReplaceEntry, StripAndReplaceType } from '../types';
export declare const SmscStripAndReplaceService: {
    getStripAndReplaceList(): Promise<StripAndReplace[]>;
    getStripAndReplaceById(id: string | number): Promise<StripAndReplace>;
    getStripAndReplaceByType(type: StripAndReplaceType): Promise<StripAndReplace>;
    saveStripAndReplace(stripAndReplace: StripAndReplace): Promise<StripAndReplace>;
    saveStripAndReplaceEntry(stripAndReplaceId: string | number, entry: StripAndReplaceEntry): Promise<StripAndReplaceEntry>;
    deleteStripAndReplaceById(id: string | number): Promise<boolean>;
    deleteStripAndReplaceEntry(stripAndReplaceId: string | number, entryId: string | number): Promise<boolean>;
    updateStripAndReplace(stripAndReplace: StripAndReplace): Promise<StripAndReplace>;
};
