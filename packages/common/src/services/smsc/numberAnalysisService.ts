import { HttpService } from "@pnmui/common/services/httpService";
import { $properties } from "@pnmui/common/services/propertiesService";
import { ChargingMethod, NumberAnalysis } from "@pnmui/common/types/smscTypes";

export const SmscNumberAnalysisService = {
  getNumberAnalysis(): Promise<NumberAnalysis[]> {
    return new Promise((resolve, reject) => {
      HttpService.get<NumberAnalysis[]>(
        `${
          $properties.value["client.smsws.base.url"] || "http://localhost:3500"
        }/numberAnalysis`,
        {}
      )
        .then((response) => {
          if (response.status === 200 || response.status === 201) {
            resolve(response.data);
          } else {
            reject(response.statusText);
          }
        })
        .catch(reject);
    });
  },

  /**
   * Gets the Number Analysis configuration by ID
   * @param id The ID of the Number Analysis configuration (always 1 for this implementation)
   * @returns A promise that resolves to the Number Analysis configuration
   */
  getNumberAnalysisById(_id: string | number = 1): Promise<NumberAnalysis> {
    return new Promise((resolve, reject) => {
      // Always use ID 1 for the single configuration
      const configId = 1;

      HttpService.get<NumberAnalysis>(
        `${
          $properties.value["client.smsws.base.url"] || "http://localhost:3500"
        }/numberAnalysis/${configId}`,
        {}
      )
        .then((response) => {
          if (response.status === 200 || response.status === 201) {
            resolve(response.data);
          } else {
            reject(response.statusText);
          }
        })
        .catch(reject);
    });
  },

  /**
   * Saves the Number Analysis configuration
   * @param numberAnalysis The Number Analysis configuration to save
   * @returns A promise that resolves to the saved Number Analysis configuration
   */
  saveNumberAnalysis(numberAnalysis: NumberAnalysis): Promise<NumberAnalysis> {
    return new Promise((resolve, reject) => {
      // Always use PUT with ID 1 for the single configuration
      const configToSave = {
        ...numberAnalysis,
        id: 1
      };

      console.log("Saving Number Analysis:", configToSave);

      const baseUrl = $properties.value["client.smsws.base.url"] || "http://localhost:3500";
      const url = `${baseUrl}/numberAnalysis/1`;

      console.log("Saving to URL:", url);

      HttpService.put<NumberAnalysis>(
        url,
        {},
        configToSave
      )
        .then((response) => {
          console.log("Save response:", response);
          if (response.status === 200 || response.status === 201) {
            resolve(response.data);
          } else {
            reject(response.statusText);
          }
        })
        .catch((error) => {
          console.error("Error saving Number Analysis:", error);
          reject(error);
        });
    });
  },

  deleteNumberAnalysisById(id: string | number): Promise<boolean> {
    return new Promise((resolve, reject) => {
      HttpService.delete(
        `${
          $properties.value["client.smsws.base.url"] || "http://localhost:3500"
        }/numberAnalysis/${id}`,
        {}
      )
        .then((response) => {
          if (response.status === 200 || response.status === 201) {
            resolve(response.data);
          } else {
            reject(response.statusText);
          }
        })
        .catch(reject);
    });
  },

  /**
   * Gets all charging methods
   * @returns A promise that resolves to an array of charging methods
   */
  getChargingMethods(): Promise<ChargingMethod[]> {
    return new Promise((resolve, reject) => {
      const baseUrl = $properties.value["client.smsws.base.url"] || "http://localhost:3500";

      HttpService.get<ChargingMethod[]>(
        `${baseUrl}/chargingMethods`,
        {}
      )
        .then((response) => {
          if (response.status === 200 || response.status === 201) {
            resolve(response.data);
          } else {
            reject(response.statusText);
          }
        })
        .catch(reject);
    });
  },

  /**
   * Reloads charging methods from the specified host
   * @param host The host to reload charging methods from
   * @returns A promise that resolves when the reload is complete
   */
  reloadChargingMethods(host: string): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!host) {
        reject(new Error("Host is required"));
        return;
      }

      // Use the correct endpoint as defined in ChargingMethodController
      const baseUrl = $properties.value["client.smsws.base.url"] || "http://localhost:3500";
      const encodedHost = encodeURIComponent(host.trim());
      const url = `${baseUrl}/chargingMethods/loadFromFileSystem?host=${encodedHost}`;

      HttpService.get(url, {})
        .then((response) => {
          if (response.status === 200 || response.status === 201 || response.status === 204) {
            resolve();
          } else {
            reject(response.statusText);
          }
        })
        .catch(reject);
    });
  },
};
