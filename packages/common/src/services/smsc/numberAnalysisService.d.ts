import { ChargingMethod, NumberAnalysis } from "@pnmui/common/types/smscTypes";
export declare const SmscNumberAnalysisService: {
    getNumberAnalysis(): Promise<NumberAnalysis[]>;
    /**
     * Gets the Number Analysis configuration by ID
     * @param id The ID of the Number Analysis configuration (always 1 for this implementation)
     * @returns A promise that resolves to the Number Analysis configuration
     */
    getNumberAnalysisById(_id?: string | number): Promise<NumberAnalysis>;
    /**
     * Saves the Number Analysis configuration
     * @param numberAnalysis The Number Analysis configuration to save
     * @returns A promise that resolves to the saved Number Analysis configuration
     */
    saveNumberAnalysis(numberAnalysis: NumberAnalysis): Promise<NumberAnalysis>;
    deleteNumberAnalysisById(id: string | number): Promise<boolean>;
    /**
     * Gets all charging methods
     * @returns A promise that resolves to an array of charging methods
     */
    getChargingMethods(): Promise<ChargingMethod[]>;
    /**
     * Reloads charging methods from the specified host
     * @param host The host to reload charging methods from
     * @returns A promise that resolves when the reload is complete
     */
    reloadChargingMethods(host: string): Promise<void>;
};
