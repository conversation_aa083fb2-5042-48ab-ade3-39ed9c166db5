import { HttpService } from "@pnmui/common/services/httpService";
import { $properties } from "@pnmui/common/services/propertiesService";
import { EnumerationOption } from '../types';

export const SmscEnumerationsService = {
  getEnumerations(): Promise<Record<string, EnumerationOption[]>> {
    return new Promise((resolve, reject) => {
      HttpService.get<Record<string, EnumerationOption[]>>(
        `${
          $properties.value["client.smsws.base.url"] || "http://localhost:3500"
        }/enumerations`,
        {}
      )
        .then((response) => {
          if (response.status === 200 || response.status === 201) {
            resolve(response.data);
          } else {
            reject(response.statusText);
          }
        })
        .catch(reject);
    });
  },
};


