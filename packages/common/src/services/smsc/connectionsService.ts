import { HttpService } from "@pnmui/common/services/httpService";
import { $properties } from "@pnmui/common/services/propertiesService";
import { SecurityService } from "@pnmui/common/services/securityService";
import { Connection } from "@pnmui/common/types/smscTypes";

export const SmscConnectionsService = {
  getConnections(): Promise<Connection[]> {
    return new Promise((resolve, reject) => {
      // Check if we have a valid base URL before making the request
      const baseUrl = $properties.value["client.smsws.base.url"];
      if (!baseUrl) {
        console.warn(`Cannot make connections request - invalid base URL: ${baseUrl}`);
        reject(new Error('Missing or invalid base URL'));
        return;
      }

      HttpService.get<Connection[]>(
        `${baseUrl}/connections`,
        {}
      )
        .then((response) => {
          if (response.status === 200 || response.status === 201) {
            resolve(response.data);
          } else {
            reject(response.statusText);
          }
        })
        .catch(error => {
          console.error('Error fetching connections:', error);
          reject(error);
        });
    });
  },

  getConnectionById(id: string | number): Promise<Connection> {
    return new Promise((resolve, reject) => {
      const baseUrl = $properties.value["client.smsws.base.url"];
      if (!baseUrl) {
        console.warn('Cannot make connection by ID request - missing base URL');
        reject(new Error('Missing base URL'));
        return;
      }

      HttpService.get<Connection>(
        `${baseUrl}/connections/${id}`,
        {}
      )
        .then((response) => {
          if (response.status === 200 || response.status === 201) {
            resolve(response.data);
          } else {
            reject(response.statusText);
          }
        })
        .catch(error => {
          console.error(`Error fetching connection with ID ${id}:`, error);
          reject(error);
        });
    });
  },

  saveConnection(connection: Connection): Promise<Connection> {
    return new Promise((resolve, reject) => {
      const baseUrl = $properties.value["client.smsws.base.url"];
      if (!baseUrl) {
        console.warn('Cannot save connection - missing base URL');
        reject(new Error('Missing base URL'));
        return;
      }

      HttpService[connection.id ? "put" : "post"]<Connection>(
        `${baseUrl}/connections${connection.id ? "/" + connection.id : ""}`,
        {},
        connection
      )
        .then((response) => {
          if (response.status === 200 || response.status === 201) {
            resolve(response.data);
          } else {
            reject(response.statusText);
          }
        })
        .catch(error => {
          console.error('Error saving connection:', error);
          reject(error);
        });
    });
  },

  saveConnectionWithName(connection: { name: string }): Promise<Connection> {
    return new Promise((resolve, reject) => {
      const baseUrl = $properties.value["client.smsws.base.url"];
      if (!baseUrl) {
        console.warn('Cannot create connection with name - missing base URL');
        reject(new Error('Missing base URL'));
        return;
      }

      HttpService.post<Connection>(
        `${baseUrl}/connections/createWithName`,
        {},
        connection
      )
        .then((response) => {
          if (response.status === 200 || response.status === 201) {
            resolve(response.data);
          } else {
            reject(response.statusText);
          }
        })
        .catch(error => {
          console.error('Error creating connection with name:', error);
          reject(error);
        });
    });
  },

  deleteConnectionById(id: string | number): Promise<boolean> {
    return new Promise((resolve, reject) => {
      const baseUrl = $properties.value["client.smsws.base.url"];
      if (!baseUrl) {
        console.warn('Cannot delete connection - missing base URL');
        reject(new Error('Missing base URL'));
        return;
      }

      HttpService.delete(
        `${baseUrl}/connections/${id}`,
        {}
      )
        .then((response) => {
          if (response.status === 200 || response.status === 201) {
            resolve(response.data);
          } else {
            reject(response.statusText);
          }
        })
        .catch(error => {
          console.error(`Error deleting connection with ID ${id}:`, error);
          reject(error);
        });
    });
  },

  loadFromFileSystem(entityType: 'connections' | 'applications', host?: string): Promise<any> {
    return new Promise((resolve, reject) => {
      if (
        !SecurityService.checkPermission(
          entityType === 'connections' ? "SMSC_CONNECTION_READ_PERMISSION" : "SMSC_APPLICATION_READ_PERMISSION"
        )
      ) {
        reject(
          `Permission Denied: You do not have access to reload ${entityType}.`
        );
        return;
      }
      const baseUrl = $properties.value["client.smsws.base.url"];
      if (!baseUrl) {
        console.warn(`Cannot reload ${entityType} - missing base URL`);
        reject(new Error('Missing base URL'));
        return;
      }

      let url = `${baseUrl}/connections/loadFromFileSystem`;
      if (host && host.trim() !== "") {
        const encodedHost = encodeURIComponent(host.trim());
        url += `?host=${encodedHost}`;
      } else {
        url += "?host=all";
      }

      HttpService.get(url, {})
        .then((response) => {
          if (response.status === 200 || response.status === 201) {
            resolve(response.data);
          } else {
            reject(response.statusText);
          }
        })
        .catch((error) => {
          console.error(`Error in loadFromFileSystem for ${entityType}:`, error);
          reject(error);
        });
    });
  },
};


