import { NumberList } from "@pnmui/common/types/smscTypes";
export declare const SmscNumberListsService: {
    getNumberLists(): Promise<NumberList[]>;
    getNumberListById(id: string | number): Promise<NumberList>;
    saveNumberList(numberList: NumberList): Promise<NumberList>;
    saveNumberListWithName(numberList: {
        name: string;
    }): Promise<NumberList>;
    deleteNumberListById(id: string | number): Promise<boolean>;
};
