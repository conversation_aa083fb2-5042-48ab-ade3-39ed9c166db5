/* eslint no-underscore-dangle: 0 */
import { HttpService } from "./httpService";
import { $properties } from "./propertiesService";
import { SmsMessage, HttpResponse } from "./types";

interface BlackoutProfile {
  id?: string | number;
  name?: string;
  description?: string;
  periods?: BlackoutPeriod[];
  [key: string]: any;
}

interface BlackoutPeriod {
  id?: string | number;
  start?: string;
  end?: string;
  weekdays?: number[];
  [key: string]: any;
}

export const SMSService = {
  getBlackoutSchedule(): Promise<BlackoutProfile[]> {
    return new Promise<BlackoutProfile[]>((resolve, reject) => {
      HttpService.get<BlackoutProfile[]>(
        `${
          $properties.value["client.sms.base.url"] || "http://localhost:8298"
        }/blackoutProfiles`,
      )
        .then((response: HttpResponse<BlackoutProfile[]>) => {
          console.log("Blackout Profiles response: ", response);
          if (response.status === 200) {
            resolve(response.data);
          } else {
            reject(response.statusText);
          }
        })
        .catch(reject);
    });
  },
  
  // Additional methods for SMS functionality
  sendSms(message: SmsMessage): Promise<SmsMessage> {
    return new Promise<SmsMessage>((resolve, reject) => {
      HttpService.post<SmsMessage>(
        `${
          $properties.value["client.sms.base.url"] || "http://localhost:8298"
        }/messages`,
        {},
        message
      )
        .then((response: HttpResponse<SmsMessage>) => {
          if (response.status === 200 || response.status === 201) {
            resolve(response.data);
          } else {
            reject(response.statusText);
          }
        })
        .catch(reject);
    });
  },
  
  getSmsStatus(messageId: string): Promise<SmsMessage> {
    return new Promise<SmsMessage>((resolve, reject) => {
      HttpService.get<SmsMessage>(
        `${
          $properties.value["client.sms.base.url"] || "http://localhost:8298"
        }/messages/${messageId}`,
      )
        .then((response: HttpResponse<SmsMessage>) => {
          if (response.status === 200) {
            resolve(response.data);
          } else {
            reject(response.statusText);
          }
        })
        .catch(reject);
    });
  }
};
