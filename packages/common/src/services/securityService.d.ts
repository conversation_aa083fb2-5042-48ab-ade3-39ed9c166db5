import { BehaviorSubject } from "rxjs";
import { AuthResponse, User } from "./types";
export declare const $userPermissions: BehaviorSubject<string[]>;
export declare const SecurityService: {
    init(): Promise<void>;
    updateUserPermissions(): Promise<void>;
    checkPermission(_permission: string): boolean;
    checkPermissionsAnd(_permissions: string[]): boolean;
    getUserPermissions(): Promise<string[]>;
    checkPermissionsOr(_permissions: string[]): boolean;
    login(username: string, _password: string): Promise<AuthResponse>;
    logout(): Promise<void>;
    getCurrentUser(): Promise<User | null>;
};
