/**
 * Array of colors for different loggers
 */
const colors: string[] = [
  "#00FFFF", "#7FFFD4", "#000000", "#0000FF", "#8A2BE2", "#A52A2A", "#DEB887",
  "#5F9EA0", "#7FFF00", "#D2691E", "#FF7F50", "#6495ED", "#DC143C", "#00FFFF",
  "#00008B", "#008B8B", "#B8860B", "#A9A9A9", "#A9A9A9", "#006400", "#8B008B",
  "#556B2F", "#FF8C00", "#9932CC", "#8B0000", "#E9967A", "#8FBC8F", "#483D8B",
  "#2F4F4F", "#2F4F4F", "#00CED1", "#9400D3", "#FF1493", "#00BFFF", "#696969",
  "#696969", "#1E90FF", "#B22222", "#228B22", "#FF00FF", "#FFD700", "#DAA520",
  "#808080", "#808080", "#008000", "#ADFF2F", "#FF69B4", "#CD5C5C", "#4B0082",
  "#7CFC00", "#ADD8E6", "#F08080", "#D3D3D3", "#D3D3D3", "#90EE90", "#FFB6C1",
  "#FFA07A", "#20B2AA", "#87CEFA", "#778899", "#778899", "#B0C4DE", "#00FF00",
  "#32CD32", "#FF00FF", "#800000", "#66CDAA", "#0000CD", "#BA55D3", "#9370D8",
  "#3CB371", "#7B68EE", "#00FA9A", "#48D1CC", "#C71585", "#191970", "#FFDEAD",
  "#000080", "#808000", "#6B8E23", "#FFA500", "#FF4500", "#DA70D6", "#EEE8AA",
  "#98FB98", "#AFEEEE", "#D87093", "#FFDAB9", "#CD853F", "#FFC0CB", "#DDA0DD",
  "#B0E0E6", "#800080", "#FF0000", "#BC8F8F", "#4169E1", "#8B4513", "#FA8072",
  "#F4A460", "#2E8B57", "#A0522D", "#C0C0C0", "#87CEEB", "#6A5ACD", "#708090",
  "#708090", "#00FF7F", "#4682B4", "#D2B48C", "#008080", "#D8BFD8", "#FF6347",
  "#40E0D0", "#EE82EE", "#F5DEB3", "#9ACD32"
];

/**
 * Map of caller names to colors
 */
const callerColors: Record<string, string> = {};

/**
 * Enhanced logging function that adds caller information and colors
 * @param values - Values to log
 */
const log = function log(...values: any[]): void {
  if (process.env.NODE_ENV === "development") {
    const stack = new Error().stack || '';
    const caller_line = stack.toString().split("\n")[2] || '';
    const callerUrl = caller_line.slice(
      caller_line.indexOf("http") >= 0 ? caller_line.indexOf("http") : 0,
      caller_line.length,
    );
    const callerMatch = caller_line.match(/at \w+\.\w+/);
    let callerName: string;
    
    if (callerMatch) {
      callerName =
        callerMatch && callerMatch.length > 0
          ? callerMatch[0].split(".").join(".")
          : "";
      callerName = callerName.replace("at ", "");
    } else {
      callerName = callerUrl.substring(
        callerUrl.lastIndexOf("/") + 1,
        callerUrl.lastIndexOf(".") >= 0 ? callerUrl.lastIndexOf(".") : callerUrl.length,
      );
    }

    if (!callerColors[callerName]) {
      callerColors[callerName] = colors[Math.floor(Math.random() * colors.length)];
    }
    
    console.info(
      `%c${callerName}:`,
      `color:${callerColors[callerName]}`,
      ...values,
      {
        stack: stack.split("\n"),
      },
      callerUrl,
    );
  }
};

// Override the default console.log
if (typeof window !== 'undefined') {
  window.console.log = log;
}

export default { log };
