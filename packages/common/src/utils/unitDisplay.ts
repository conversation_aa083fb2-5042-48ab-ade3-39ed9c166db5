/* eslint no-underscore-dangle: 0 */

/**
 * Type for volume display values
 */
type VolumeDisplay = string | "UNLIMITED";

/**
 * Utility class for displaying units (time, bytes, etc.)
 */
export class UnitDisplayUtils {
  /**
   * Converts seconds to a human-readable time format
   * @param secs - Number of seconds or "UNLIMITED"
   * @param minorUnit - Number of minor units to display
   * @returns Formatted time string
   */
  static secondsToHighestUnitDisplay(secs: number | "UNLIMITED", minorUnit: number): string {
    if (secs === "UNLIMITED") {
      return "Unlimited";
    }
    if (secs === 0) {
      return "0";
    }
    let minorUnitCounter = 0;
    const day = Math.floor(secs / 86400);
    const hr = Math.floor((secs - day * 86400) / 3600);
    const min = Math.floor((secs - day * 86400 - hr * 3600) / 60);
    const sec = Math.floor(secs - day * 86400 - hr * 3600 - min * 60);
    let time = "";
    if (day >= 1) {
      time = `${day}day `;
      minorUnitCounter += minorUnitCounter;
      // minorUnitCounter++;
    }
    if (hr >= 1 && minorUnitCounter <= minorUnit) {
      time = `${time + hr}hr `;
      minorUnitCounter += minorUnitCounter;
      // minorUnitCounter++;
    }
    if (min >= 1 && minorUnitCounter <= minorUnit) {
      time = `${time + min}min `;
      minorUnitCounter += minorUnitCounter;
      // minorUnitCounter++;
    }
    if (sec >= 1 && minorUnitCounter <= minorUnit) {
      time = `${time + sec}sec `;
      minorUnitCounter += minorUnitCounter;
      // minorUnitCounter++;
    }
    return time;
  }

  /**
   * Converts bytes to a human-readable format with appropriate units
   * @param bytes - Number of bytes or "UNLIMITED"
   * @param decimalPlaces - Number of decimal places to display
   * @returns Formatted byte string
   */
  static bytesToHighestUnitDisplay(bytes: number | "UNLIMITED", decimalPlaces: number): string | null {
    if (bytes === "UNLIMITED") {
      return "Unlimited";
    }
    if (bytes != null && decimalPlaces != null) {
      if (bytes === 0) {
        return "0";
      }
      if (bytes <= 1024) {
        return `${bytes}bytes`;
      }
      const sizes = ["bytes", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"];

      const i = parseInt(
        Math.floor(Math.log(parseInt(bytes.toString(), 10)) / Math.log(1024)).toString(),
        10,
      );

      return (
        (bytes / Math.pow(1024, i)).toFixed(parseInt(decimalPlaces.toString(), 10)) + sizes[i]
      );
    }
    return null;
  }

  /**
   * Gets an array of volume values
   * @returns Array of volume values
   */
  static getVolumeValues(): string[] {
    return [
      "1byte",
      "2bytes",
      "4bytes",
      "8bytes",
      "16bytes",
      "32bytes",
      "64bytes",
      "128bytes",
      "256bytes",
      "512bytes",
      "1KB",
      "2KBs",
      "4KBs",
      "8KBs",
      "16KBs",
      "32KBs",
      "64KBs",
      "128KBs",
      "256KBs",
      "512KBs",
      "1MB",
      "2MBs",
      "5MBs",
      "10MBs",
      "20MBs",
      "30MBs",
      "40MB",
      "50MBs",
      "100MBs",
      "200MBs",
      "500MBs",
      "1GB",
      "2GBs",
      "3GBs",
      "4GBs",
      "5GBs",
      "6GBs",
      "7GBs",
      "8GBs",
      "9GBs",
      "10GBs",
      "11GBs",
      "12GBs",
      "13GBs",
      "14GBs",
      "15GBs",
      "16GBs",
      "17GBs",
      "18GBs",
      "19GBs",
      "20GBs",
      "21GBs",
      "22GBs",
      "23GBs",
      "24GBs",
      "25GBs",
      "26GBs",
      "27GBs",
      "28GBs",
      "29GBs",
      "30GBs",
      "31GBs",
      "32GBs",
      "33GBs",
      "34GBs",
      "35GBs",
      "36GBs",
      "37GBs",
      "38GBs",
      "39GBs",
      "40GBs",
      "41GBs",
      "42GBs",
      "43GBs",
      "44GBs",
      "45GBs",
      "46GBs",
      "47GBs",
      "48GBs",
      "49GBs",
      "50GBs",
      "100GBs",
      "200GBs",
      "300GBs",
      "400GBs",
      "500GBs",
      "600GBs",
      "700GBs",
      "800GBs",
      "900GBs",
      "1000GBs",
      "UNLIMITED",
    ];
  }

  /**
   * Gets a volume value from a slider value
   * @param value - Slider value (index)
   * @returns Volume value
   */
  static getVolumeFromSliderValue(value: number): string {
    const volumeValues = this.getVolumeValues();
    return volumeValues[value];
  }

  /**
   * Converts a volume display string to bytes
   * @param volumeDisplay - Volume display string
   * @returns Number of bytes or "UNLIMITED"
   */
  static volumeDisplayToBytes(volumeDisplay: VolumeDisplay): number | "UNLIMITED" {
    if (volumeDisplay === "UNLIMITED") {
      return volumeDisplay;
    }
    const arrayOfVolumes = volumeDisplay.split(" ");
    let gbs = 0;
    let mbs = 0;
    let kbs = 0;
    let bytes = 0;
    
    for (let i = 0; i < arrayOfVolumes.length; i++) {
      if (arrayOfVolumes[i].indexOf("GB") !== -1) {
        gbs = parseInt(
          arrayOfVolumes[i].substr(0, arrayOfVolumes[i].indexOf("GB")),
          10,
        );
      } else if (arrayOfVolumes[i].indexOf("MB") !== -1) {
        mbs = parseInt(
          arrayOfVolumes[i].substr(0, arrayOfVolumes[i].indexOf("MB")),
          10,
        );
      } else if (arrayOfVolumes[i].indexOf("KB") !== -1) {
        kbs = parseInt(
          arrayOfVolumes[i].substr(0, arrayOfVolumes[i].indexOf("KB")),
          10,
        );
      } else if (arrayOfVolumes[i].indexOf("byte") !== -1) {
        bytes = parseInt(
          arrayOfVolumes[i].substr(0, arrayOfVolumes[i].indexOf("byte")),
          10,
        );
      }
    }
    bytes += gbs * 1073741824 + mbs * 1048576 + kbs * 1024;
    return bytes;
  }

  /**
   * Gets a slider value from a volume
   * @param value - Volume value
   * @param keepPlural - Whether to keep plural forms
   * @returns Slider value (index)
   */
  static getSliderValueFromVolume(value: string, keepPlural?: boolean): number {
    if (!keepPlural) value = value.replace(/^\s+|\s+$/g, "");
    const volumeValues = this.getVolumeValues();
    return volumeValues.indexOf(value);
  }
}
