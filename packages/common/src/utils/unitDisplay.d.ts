/**
 * Type for volume display values
 */
type VolumeDisplay = string | "UNLIMITED";
/**
 * Utility class for displaying units (time, bytes, etc.)
 */
export declare class UnitDisplayUtils {
    /**
     * Converts seconds to a human-readable time format
     * @param secs - Number of seconds or "UNLIMITED"
     * @param minorUnit - Number of minor units to display
     * @returns Formatted time string
     */
    static secondsToHighestUnitDisplay(secs: number | "UNLIMITED", minorUnit: number): string;
    /**
     * Converts bytes to a human-readable format with appropriate units
     * @param bytes - Number of bytes or "UNLIMITED"
     * @param decimalPlaces - Number of decimal places to display
     * @returns Formatted byte string
     */
    static bytesToHighestUnitDisplay(bytes: number | "UNLIMITED", decimalPlaces: number): string | null;
    /**
     * Gets an array of volume values
     * @returns Array of volume values
     */
    static getVolumeValues(): string[];
    /**
     * Gets a volume value from a slider value
     * @param value - Slider value (index)
     * @returns Volume value
     */
    static getVolumeFromSliderValue(value: number): string;
    /**
     * Converts a volume display string to bytes
     * @param volumeDisplay - Volume display string
     * @returns Number of bytes or "UNLIMITED"
     */
    static volumeDisplayToBytes(volumeDisplay: VolumeDisplay): number | "UNLIMITED";
    /**
     * Gets a slider value from a volume
     * @param value - Volume value
     * @param keepPlural - Whether to keep plural forms
     * @returns Slider value (index)
     */
    static getSliderValueFromVolume(value: string, keepPlural?: boolean): number;
}
export {};
