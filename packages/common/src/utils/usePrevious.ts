import { useEffect, useRef } from "react";

/**
 * Custom hook that returns the previous value of a variable
 * @param value - The value to track
 * @returns The previous value of the variable
 */
function usePrevious<T>(value: T): T | undefined {
  const ref = useRef<T>();
  
  useEffect(() => {
    ref.current = value; // assign the value of ref to the argument
  }, [value]); // this code will run when the value of 'value' changes
  
  return ref.current; // in the end, return the current ref value.
}

export default usePrevious;
