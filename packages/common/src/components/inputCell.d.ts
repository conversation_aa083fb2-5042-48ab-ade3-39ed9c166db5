import { CSSProperties, FC } from "react";
interface DataItem {
    id?: string | number;
    inEdit?: boolean;
    [key: string]: any;
}
interface ChangeEvent {
    dataIndex: number;
    dataItem: DataItem;
    field: string;
    value: string | number;
}
interface InputCellProps {
    dataItem: DataItem;
    field?: string;
    onChange?: (event: ChangeEvent) => void;
    dataIndex?: number;
    error?: string;
    alwaysEditable?: boolean;
    type?: string;
    sx?: any;
    style?: CSSProperties;
    debounceDelay?: number;
    min?: number;
    max?: number;
    step?: number;
    id?: string;
    colSpan?: number;
    ariaColumnIndex?: number;
    isSelected?: boolean;
    columnIndex?: number;
}
declare const InputCell: FC<InputCellProps>;
export default InputCell;
