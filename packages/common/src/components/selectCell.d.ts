import { SelectChangeEvent } from "@mui/material";
import { FC } from "react";
interface DataItem {
    id?: string | number;
    value?: string | number;
    inEdit?: boolean;
    [key: string]: any;
}
interface Option {
    id?: string | number;
    value?: string | number;
    name?: string;
    displayText?: string;
    label?: string;
    [key: string]: any;
}
interface ChangeEvent {
    dataIndex: number;
    dataItem: DataItem;
    field: string;
    syntheticEvent: SelectChangeEvent<string | number>;
    value: any;
}
interface SelectCellProps {
    dataItem: DataItem;
    field?: string;
    dataIndex?: number;
    options?: Option[];
    onChange?: (event: ChangeEvent) => void;
    valueFunc?: (option: Option) => string | number;
    displayFunc?: (option: Option) => string;
    error?: string;
    optionLabelField?: string;
    style?: React.CSSProperties;
}
declare const SelectCell: FC<SelectCellProps>;
export default SelectCell;
