import React, { ReactElement } from 'react';
import { Switch } from "@mui/material";
import { useTableKeyboardNavigation } from "@progress/kendo-react-data-tools";

// Import the constant directly to avoid type issues
const GRID_COL_INDEX_ATTRIBUTE = 'data-grid-col-index';

interface DataItem {
  [key: string]: any;
  inEdit?: boolean;
}

interface ChangeEvent {
  dataIndex: number;
  dataItem: DataItem;
  field: string;
  syntheticEvent: React.ChangeEvent<HTMLInputElement>;
  value: boolean;
}

interface BooleanYesNoCellProps {
  field?: string;
  dataItem: DataItem;
  id?: string;
  onChange?: (event: ChangeEvent) => void;
  colSpan?: number;
  ariaColumnIndex?: number;
  isSelected?: boolean;
  columnIndex?: number;
  render?: (content: ReactElement, props: BooleanYesNoCellProps) => ReactElement;
}

const BooleanYesNoCell: React.FC<BooleanYesNoCellProps> = (props) => {
  const field = props.field || "";
  const { dataItem } = props;
  const value = dataItem[field];
  const navigationAttributes = useTableKeyboardNavigation(props.id || '');

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>): void => {
    dataItem[field] = e.target.checked;
    if (props.onChange) {
      props.onChange({
        dataIndex: 0,
        dataItem: dataItem,
        field: field,
        syntheticEvent: e,
        value: e.target.checked,
      });
    }
  };

  const content = (
    <td
      colSpan={props.colSpan}
      role={"gridcell"}
      aria-colindex={props.ariaColumnIndex}
      aria-selected={props.isSelected}
      {...{
        [GRID_COL_INDEX_ATTRIBUTE]: props.columnIndex,
      }}
      {...navigationAttributes}
    >
      {dataItem.inEdit ? (
        <Switch size="small" checked={!!value} onChange={handleChange} />
      ) : value === null ? (
        "No"
      ) : value ? (
        "Yes"
      ) : (
        "No"
      )}
    </td>
  );

  return props.render ? props.render(content, props) : content;
};

export default BooleanYesNoCell;
