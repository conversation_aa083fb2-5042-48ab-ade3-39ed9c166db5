import { ReactNode, CSSProperties } from 'react';
export interface CommonProps {
    className?: string;
    style?: CSSProperties;
    id?: string;
}
export interface WithChildren {
    children?: ReactNode;
}
export interface ErrorInfo {
    message: string;
    stack?: string;
    componentStack?: string;
}
export interface TableColumn {
    field: string;
    title: string;
    type?: string;
    width?: string | number;
    editable?: boolean;
    hidden?: boolean;
    render?: (rowData: any) => ReactNode;
    cellStyle?: CSSProperties | ((rowData: any) => CSSProperties);
    headerStyle?: CSSProperties;
    [key: string]: any;
}
export interface TableOptions {
    search?: boolean;
    sorting?: boolean;
    filtering?: boolean;
    paging?: boolean;
    exportButton?: boolean;
    selection?: boolean;
    grouping?: boolean;
    columnsButton?: boolean;
    pageSize?: number;
    pageSizeOptions?: number[];
    showTitle?: boolean;
    toolbar?: boolean;
    emptyRowsWhenPaging?: boolean;
    [key: string]: any;
}
export interface FormField {
    name: string;
    label: string;
    type?: string;
    required?: boolean;
    options?: Array<{
        label: string;
        value: any;
    }>;
    defaultValue?: any;
    fullWidth?: boolean;
    multiline?: boolean;
    rows?: number;
    disabled?: boolean;
    [key: string]: any;
}
