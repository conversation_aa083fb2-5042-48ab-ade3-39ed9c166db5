import { FC } from "react";
interface ErrorResponseData {
    errors?: Record<string, string>;
    data?: any;
    statusText?: string;
    message?: string;
}
interface ErrorResponse {
    response?: ErrorResponseData;
    message?: string;
}
interface ErrorsDisplayProps {
    errorResponse: ErrorResponse | null;
    keyPrefix: string;
}
declare const ErrorsDisplay: FC<ErrorsDisplayProps>;
export default ErrorsDisplay;
