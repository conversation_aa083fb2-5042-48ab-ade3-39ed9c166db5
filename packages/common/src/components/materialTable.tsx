import {
  DragDrop<PERSON>ontext,
  Draggable,
  Droppable,
  DropResult,
} from "@hello-pangea/dnd";
import FirstPageIcon from "@mui/icons-material/FirstPage";
import LastPageIcon from "@mui/icons-material/LastPage";
import NavigateBeforeIcon from "@mui/icons-material/NavigateBefore";
import NavigateNextIcon from "@mui/icons-material/NavigateNext";
import {
  Checkbox,
  IconButton,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
} from "@mui/material";
import {
  DataGrid,
  GridColDef,
  gridPageCountSelector,
  gridPageSelector,
  GridRenderCellParams,
  GridRowParams,
  useGridApiContext,
  useGridSelector,
} from "@mui/x-data-grid";
import React, {
  ChangeEvent,
  forwardRef,
  MouseEvent,
  ReactElement,
  Ref,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from "react";

// Define types for the component
interface CustomPaginationProps {}

interface TableColumn {
  field?: string;
  title?: string;
  render?: (rowData: any) => ReactElement | string | null;
  width?: number | string;
  [key: string]: any;
}

interface TableAction {
  icon?: React.ComponentType;
  tooltip?: string;
  onClick?: (event: MouseEvent, rowData: any) => void;
  render?: () => ReactElement | null;
  [key: string]: any;
}

interface TableOptions {
  pageSize?: number;
  hideCheckboxes?: boolean;
  [key: string]: any;
}

interface TableData {
  data: any[];
  totalCount: number;
}

interface MaterialTableProps {
  data?: (params: { page: number; pageSize: number }) => Promise<TableData>;
  columns: TableColumn[];
  options: TableOptions;
  actions?: (TableAction | (() => TableAction))[];
  onRowClick?: (event: MouseEvent, rowData: any) => void;
  onSelectionChange?: (selectedRows: { id: string | number }[]) => void;
  tableRef?: Ref<TableRefHandle>;
  enableDragAndDrop?: boolean;
  rows?: any[];
  onRowOrderChange?: (newRows: any[]) => void;
}

interface TableRefHandle {
  onQueryChange: (query: { page: number; pageSize: number } | null) => void;
  refreshData: () => void;
}

const CustomPagination: React.FC<CustomPaginationProps> = () => {
  const apiRef = useGridApiContext();
  const page = useGridSelector(apiRef, gridPageSelector);
  const pageCount = useGridSelector(apiRef, gridPageCountSelector);
  const { state } = apiRef.current;
  const paginationModel = state.pagination.paginationModel ?? {
    page: 0,
    pageSize: 20,
  };
  const pageSize = paginationModel.pageSize || 20;
  const rowCount = state.rows.totalRowCount || 0;

  const firstRow =
    typeof page === "number" && typeof pageSize === "number" && rowCount > 0
      ? page * pageSize + 1
      : 0;
  const lastRow =
    typeof page === "number" && typeof pageSize === "number" && rowCount > 0
      ? Math.min((page + 1) * pageSize, rowCount)
      : 0;

  const handlePageSizeChange = (e: ChangeEvent<HTMLSelectElement>) => {
    const newPageSize = Number(e.target.value);
    const currentPage = typeof page === "number" ? page : 0;
    apiRef.current.setPaginationModel({
      page: currentPage,
      pageSize: newPageSize,
    });
  };

  return (
    <div
      style={{
        display: "flex",
        justifyContent: "space-between",
        alignItems: "center",
        padding: "12px 24px",
        color: "rgba(0, 0, 0, 0.54)",
        height: "64px",
        boxSizing: "border-box",
        gap: "100px",
      }}
    >
      <div style={{ display: "flex", alignItems: "end" }}>
        <Typography variant="body2" style={{ color: "rgba(0, 0, 0, 0.54)" }}>
          Rows per page:
        </Typography>
        <div
          style={{ display: "flex", alignItems: "center", marginLeft: "24px" }}
        >
          <select
            value={pageSize}
            onChange={handlePageSizeChange}
            style={{
              border: "none",
              backgroundColor: "transparent",
              fontSize: "inherit",
              fontFamily: "inherit",
              color: "rgba(0, 0, 0, 0.54)",
              WebkitAppearance: "none",
              MozAppearance: "none",
              appearance: "none",
              paddingRight: "0",
              cursor: "pointer",
              outline: "none",
              width: "auto",
            }}
          >
            {[10, 20, 50].map((size) => (
              <option key={size} value={size}>
                {size}
              </option>
            ))}
          </select>
          <span
            style={{
              color: "rgba(0, 0, 0, 0.54)",
              fontSize: "0.7rem",
              marginLeft: "2px",
              position: "relative",
              top: "0px",
            }}
          >
            ▼
          </span>
        </div>
      </div>
      <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
        <Typography variant="body2" style={{ marginRight: "32px" }}>
          {`${firstRow}  –  ${lastRow}  of  ${rowCount}`}
        </Typography>
        <IconButton
          onClick={() =>
            apiRef.current.setPaginationModel({ page: 0, pageSize })
          }
          disabled={page === 0}
          size="small"
          style={{ padding: "4px" }}
        >
          <FirstPageIcon />
        </IconButton>
        <IconButton
          onClick={() =>
            apiRef.current.setPaginationModel({ page: page - 1, pageSize })
          }
          disabled={page === 0}
          size="small"
          style={{ padding: "4px" }}
        >
          <NavigateBeforeIcon />
        </IconButton>
        <IconButton
          onClick={() =>
            apiRef.current.setPaginationModel({ page: page + 1, pageSize })
          }
          disabled={
            !(typeof page === "number" && typeof pageCount === "number") ||
            page >= pageCount - 1
          }
          size="small"
          style={{ padding: "4px" }}
        >
          <NavigateNextIcon />
        </IconButton>
        <IconButton
          onClick={() =>
            apiRef.current.setPaginationModel({ page: pageCount - 1, pageSize })
          }
          disabled={
            !(typeof page === "number" && typeof pageCount === "number") ||
            page >= pageCount - 1
          }
          size="small"
          style={{ padding: "4px" }}
        >
          <LastPageIcon />
        </IconButton>
      </div>
    </div>
  );
};

const MaterialTable = forwardRef<unknown, MaterialTableProps>(
  (
    {
      data,
      columns,
      options,
      actions = [],
      onRowClick,
      onSelectionChange,
      tableRef,
      enableDragAndDrop = false,
      rows: externalRows,
      onRowOrderChange,
    },
    _ref
  ) => {
    const [internalRows, setInternalRows] = useState<any[]>([]);
    const [page, setPage] = useState<number>(0);
    const [pageSize, setPageSize] = useState<number>(options.pageSize || 20);
    const [totalCount, setTotalCount] = useState<number>(0);
    const [selectedRows, setSelectedRows] = useState<(string | number)[]>([]);
    const [loading, setLoading] = useState<boolean>(!enableDragAndDrop);
    const isInitialMount = useRef<boolean>(true);

    const [dndRows, setDndRows] = useState<any[]>([]);

    // Convert IDs to strings for drag and drop
    useEffect(() => {
      if (enableDragAndDrop && externalRows) {
        const rowsWithId = externalRows.map((row, index) => {
          // Ensure dndId is always a string
          const stringId = row.id !== undefined ? String(row.id) :
                          (row.name !== undefined ? String(row.name) : `dnd-row-${index}`);

          return {
            ...row,
            dndId: stringId
          };
        });
        setDndRows(rowsWithId);
      }
    }, [externalRows, enableDragAndDrop]);

    useEffect(() => {
      if (!enableDragAndDrop) {
        console.log("Current pageSize:", pageSize);
        setPage(0);
      }
    }, [pageSize, enableDragAndDrop]);

    const fetchData = async (): Promise<void> => {
      if (!data) return;
      setLoading(true);
      try {
        const result = await data({ page, pageSize });
        console.log("result", result);
        if (result && Array.isArray(result.data)) {
          setInternalRows(
            result.data.map((row) => ({ ...row, id: row.id || row.name }))
          );
          setTotalCount(result.totalCount);
        } else {
          console.error("Invalid data format received:", result);
          setInternalRows([]);
          setTotalCount(0);
        }
      } catch (err) {
        console.error("Error fetching data:", err);
        setInternalRows([]);
        setTotalCount(0);
      } finally {
        setLoading(false);
      }
    };

    useEffect(() => {
      if (!enableDragAndDrop) {
        if (isInitialMount.current) {
          isInitialMount.current = false;
          fetchData();
        } else {
          fetchData();
        }
      }
    }, [page, pageSize, data, enableDragAndDrop]);

    useImperativeHandle(tableRef, () => ({
      onQueryChange: (query: { page: number; pageSize: number } | null) => {
        if (!enableDragAndDrop) {
          console.log("tableRef query", query);
          setPage(query ? query.page : 0);
          setPageSize(query ? query.pageSize : options.pageSize || 20);
        }
      },
      refreshData: () => {
        if (!enableDragAndDrop) {
          fetchData();
          setSelectedRows([]);
        }
      },
    }));

    const handleDragEnd = (result: DropResult) => {
      if (!result.destination) return;
      if (!onRowOrderChange) return;

      const items = Array.from(dndRows);
      const [reorderedItem] = items.splice(result.source.index, 1);
      items.splice(result.destination.index, 0, reorderedItem);

      setDndRows(items);

      onRowOrderChange(items.map(({ dndId, ...rest }) => rest));
    };

    const handleSelectionChange = (
      id: string | number,
      isSelected: boolean
    ): void => {
      let newSelectedRows: (string | number)[];
      if (isSelected) {
        newSelectedRows = [...selectedRows, id];
      } else {
        newSelectedRows = selectedRows.filter((rowId) => rowId !== id);
      }
      setSelectedRows(newSelectedRows);
      if (onSelectionChange) {
        onSelectionChange(
          newSelectedRows.map((rowId) => ({
            id: rowId,
          }))
        );
      }
    };

    const renderCheckbox = (params: GridRenderCellParams): ReactElement => (
      <Checkbox
        checked={selectedRows.includes(params.id as string | number)}
        onChange={(e: ChangeEvent<HTMLInputElement>) =>
          handleSelectionChange(params.id as string | number, e.target.checked)
        }
        onClick={(e: MouseEvent) => e.stopPropagation()}
      />
    );

    const renderActions = (params: GridRenderCellParams): ReactElement => (
      <div>
        {actions.map((actionItem, index) => {
          const action: TableAction =
            typeof actionItem === "function" ? actionItem() : actionItem;

          return action.icon ? (
            <IconButton
              key={index}
              onClick={(e: MouseEvent) => {
                e.preventDefault();
                e.stopPropagation();
                action.onClick && action.onClick(e, params.row);
              }}
              title={action.tooltip}
            >
              {React.createElement(action.icon)}
            </IconButton>
          ) : action.render ? (
            <React.Fragment key={index}>{action.render()}</React.Fragment>
          ) : (
            <React.Fragment key={index}></React.Fragment>
          );
        })}
      </div>
    );

    if (enableDragAndDrop) {
      return (
        <TableContainer component={Paper} elevation={2}>
          <DragDropContext onDragEnd={handleDragEnd}>
            <Table sx={{ minWidth: 650 }} aria-label="draggable table">
              <TableHead>
                <TableRow>
                  <TableCell style={{ width: "50px" }} />
                  {columns.map((col) => (
                    <TableCell
                      key={col.field || col.title}
                      align={col.align || "left"}
                      style={{ width: col.width || "auto" }}
                    >
                      {col.title}
                    </TableCell>
                  ))}
                </TableRow>
              </TableHead>
              <Droppable droppableId="droppable-table">
                {(provided) => (
                  <TableBody
                    ref={provided.innerRef}
                    {...provided.droppableProps}
                  >
                    {dndRows.map((row, index) => (
                      <Draggable
                        key={row.dndId}
                        draggableId={row.dndId}
                        index={index}
                      >
                        {(providedDraggable) => (
                          <TableRow
                            ref={providedDraggable.innerRef}
                            {...providedDraggable.draggableProps}
                            sx={{
                              "&:last-child td, &:last-child th": { border: 0 },
                            }}
                          >
                            <TableCell
                              {...providedDraggable.dragHandleProps}
                              style={{ cursor: "grab", width: "50px" }}
                              onClick={(e) => e.stopPropagation()}
                            >
                              <span style={{ fontSize: '24px', fontWeight: 'bold' }}>☰</span>
                            </TableCell>
                            {columns.map((col) => (
                              <TableCell
                                key={col.field || col.title}
                                align={col.align || "left"}
                                onClick={(e) =>
                                  onRowClick && onRowClick(e as any, row)
                                }
                                style={{
                                  cursor: onRowClick ? "pointer" : "default",
                                }}
                              >
                                {col.render
                                  ? col.render(row)
                                  : row[col.field || ""] ?? ""}
                              </TableCell>
                            ))}
                          </TableRow>
                        )}
                      </Draggable>
                    ))}
                    {provided.placeholder}
                  </TableBody>
                )}
              </Droppable>
            </Table>
          </DragDropContext>
        </TableContainer>
      );
    } else {
      const modifiedColumns: GridColDef[] = [
        ...columns.map((col) => ({
          key: col.field || col.title,
          field: col.field || col.title || "",
          headerName: col.title || "",
          flex: 1,
          renderCell: col.render
            ? (params: GridRenderCellParams) =>
                col.render && col.render(params.row)
            : undefined,
        })),
      ];

      if (!options.hideCheckboxes) {
        modifiedColumns.unshift({
          field: "selection",
          headerName: "",
          width: 50,
          renderCell: renderCheckbox,
          sortable: false,
          filterable: false,
        });
      }

      if (actions && actions.length) {
        modifiedColumns.push({
          field: "actions",
          headerName: "",
          width: 100,
          renderCell: renderActions,
          sortable: false,
          filterable: false,
        });
      }

      return (
        <Paper elevation={2} style={{ height: "auto", width: "100%" }}>
          <DataGrid
            rows={internalRows}
            columns={modifiedColumns}
            pagination={true}
            paginationMode="server"
            rowCount={totalCount || 0}
            paginationModel={{ page, pageSize }}
            onPaginationModelChange={(model: {
              page: number;
              pageSize: number;
            }) => {
              setPage(model.page);
              setPageSize(model.pageSize);
            }}
            pageSizeOptions={[10, 20, 50]}
            disableRowSelectionOnClick
            getRowId={(row: any) =>
              row.id ||
              row.msisdn ||
              row.identifier ||
              row.name ||
              Math.random()
            }
            loading={loading}
            slots={{
              pagination: CustomPagination,
            }}
            sx={{
              "& .MuiDataGrid-columnHeaders": {
                fontWeight: "bold",
                paddingLeft: "1em",
              },
              "& .MuiDataGrid-cell": {
                paddingLeft: "1em",
                cursor: onRowClick ? "pointer" : "default",
              },
              "& .MuiDataGrid-cell:focus": {
                outline: "none",
              },
              "& .MuiDataGrid-row": {
                maxHeight: "none !important",
              },
              "& .MuiDataGrid-renderingZone": {
                maxHeight: "none !important",
              },
              "& .MuiDataGrid-virtualScroller": {
                overflowY: "hidden",
              },
            }}
            disableColumnMenu
            onRowClick={(params: GridRowParams, event: any) => {
              const target = event.target as HTMLElement;
              const closestButton = target.closest("button");
              const closestCheckbox = target.closest(".MuiCheckbox-root");

              if (closestCheckbox || closestButton) {
                return;
              }
              if (onRowClick) {
                onRowClick(event, params.row);
              }
            }}
          />
        </Paper>
      );
    }
  }
);

export default MaterialTable;
