import { FC } from "react";
import DialogTitle from "@mui/material/DialogTitle";
import Dialog from "@mui/material/Dialog";
import { DialogContent } from "@mui/material";

interface ErrorDialogProps {
  errorMessage: string;
  onClose?: () => void;
}

export const ErrorDialog: FC<ErrorDialogProps> = (props) => {
  return (
    <Dialog
      className="error-dialog"
      aria-labelledby="error-dialog-title"
      open={!!props.errorMessage}
      onClose={props.onClose}
    >
      <DialogTitle id="error-dialog-title">Error</DialogTitle>
      <DialogContent>
        <span
          className="error"
          dangerouslySetInnerHTML={{ __html: props.errorMessage }}
        />
      </DialogContent>
    </Dialog>
  );
};
