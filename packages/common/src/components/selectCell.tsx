import { FormControl, MenuItem, Select, SelectChangeEvent } from "@mui/material";
import { FC, useState } from "react";

interface DataItem {
  id?: string | number;
  value?: string | number;
  inEdit?: boolean;
  [key: string]: any;
}

interface Option {
  id?: string | number;
  value?: string | number;
  name?: string;
  displayText?: string;
  label?: string;
  [key: string]: any;
}

interface ChangeEvent {
  dataIndex: number;
  dataItem: DataItem;
  field: string;
  syntheticEvent: SelectChangeEvent<string | number>;
  value: any;
}

interface SelectCellProps {
  dataItem: DataItem;
  field?: string;
  dataIndex?: number;
  options?: Option[];
  onChange?: (event: ChangeEvent) => void;
  valueFunc?: (option: Option) => string | number;
  displayFunc?: (option: Option) => string;
  error?: string;
  optionLabelField?: string;
  style?: React.CSSProperties;
}

const SelectCell: FC<SelectCellProps> = (props) => {
  const { dataItem, field = "", displayFunc, options = [], optionLabelField = "name", style } = props;
  // We use this state to track changes, even though we don't directly read it in the render
  const [, setSelectedValue] = useState<string | number | null>(null);

  if (!dataItem) {
    console.warn("SelectCell received null or undefined dataItem");
    return null;
  }

  const dataValue = dataItem[field];

  // Get the current value for the select
  const getCurrentValue = () => {
    if (dataValue === undefined || dataValue === null) return "";

    if (typeof dataValue === "object") {
      return dataValue?.id || dataValue?.value || "";
    }

    return dataValue;
  };

  // Try to get display text, falling back to raw value
  let displayText = "";
  try {
    if (dataValue !== undefined && dataValue !== null) {
      if (displayFunc) {
        displayText = String(displayFunc(dataValue));
      } else if (typeof dataValue === "object") {
        displayText = String(
          dataValue?.[optionLabelField] ||
          dataValue?.name ||
          dataValue?.displayText ||
          dataValue?.label ||
          dataValue?.value ||
          dataValue
        );
      } else {
        // Find the option that matches the value
        const option = options.find(opt =>
          opt.id === dataValue ||
          opt.value === dataValue
        );

        if (option) {
          displayText = String(
            option[optionLabelField] ||
            option.name ||
            option.displayText ||
            option.label ||
            option.value ||
            dataValue
          );
        } else {
          displayText = String(dataValue);
        }
      }
    }
  } catch (e) {
    console.error("Error getting display text in SelectCell:", e);
    displayText = String(dataValue); // Fallback
  }

  const handleChange = (event: SelectChangeEvent<unknown>) => {
    const value = event.target.value;
    setSelectedValue(value as string | number);

    if (props.onChange) {
      // Find the selected option
      const selectedOption = options.find(opt =>
        opt.id === value ||
        opt.value === value
      );

      // Cast the event to the expected type
      const typedEvent = {
        ...event,
        target: {
          ...event.target,
          value: value as string | number
        }
      } as SelectChangeEvent<string | number>;

      props.onChange({
        dataIndex: props.dataIndex || 0,
        dataItem: props.dataItem,
        field: field,
        syntheticEvent: typedEvent,
        value: selectedOption || value
      });
    }
  };

  return (
    <td>
      <div style={{ position: 'relative', ...style }}>
        {dataItem.inEdit ? (
          <FormControl fullWidth size="small" error={!!props.error}>
            <Select
              value={getCurrentValue()}
              onChange={handleChange}
              displayEmpty
              style={{ minWidth: '120px' }}
            >
              <MenuItem value="">
                <em>None</em>
              </MenuItem>
              {options.map((option, index) => (
                <MenuItem
                  key={option.id || option.value || index}
                  value={option.id || option.value}
                >
                  {option[optionLabelField] || option.name || option.displayText || option.label || option.value}
                </MenuItem>
              ))}
            </Select>
            {props.error && (
              <div style={{ color: 'red', fontSize: '0.75rem', marginTop: '0.25rem' }}>
                {props.error}
              </div>
            )}
          </FormControl>
        ) : (
          <span>{displayText}</span>
        )}
      </div>
    </td>
  );
};

export default SelectCell;
