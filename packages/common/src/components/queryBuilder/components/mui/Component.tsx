import { ReactNode } from "react";
import { Paper } from "@mui/material";
import styled from "styled-components";

const Container = styled(Paper)`
  display: grid;
  grid-template-columns: 1fr auto;
  align-items: center;
  margin: 0.5rem 0;
`;

const Content = styled.div`
  padding: 1rem;
  display: grid;
  grid-auto-columns: min-content;
  grid-auto-flow: column;
  grid-gap: 0.5rem;
`;

const Header = styled.div`
  padding: 1rem;
  display: grid;
  grid-auto-columns: min-content;
  grid-auto-flow: column;
  justify-content: flex-end;
`;

interface ComponentWrapperProps {
  children?: ReactNode;
  controls?: ReactNode;
}

export function Component({ children, controls }: ComponentWrapperProps) {
  return (
    <Container>
      <Content>{children}</Content>
      <Header>{controls}</Header>
    </Container>
  );
}
