import { ChangeEvent } from "react";
import { Input as InputBase } from "@mui/material";
import styled from "styled-components";
import { ValueEditorProps } from "../../types";

const Container = styled.div`
  input {
    width: auto;
  }
`;

const InputWithBorder = styled(InputBase)`
  border: 1px solid #dadada;
  border-radius: 5px;
  padding: 5px;
`;

interface InputProps extends ValueEditorProps {
  type?: string;
}

export function Input({ handleOnChange: onChange, value, type = "text" }: InputProps) {
  const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
    if (onChange) {
      onChange(event.target.value);
    }
  };

  return (
    <Container>
      <InputWithBorder onChange={handleChange} value={value || ""} type={type} />
    </Container>
  );
}
