import { ReactNode } from "react";
import { Button } from "@mui/material";
import { QueryBuilderCommonProps } from "../../types";

interface GroupHeaderOptionProps extends QueryBuilderCommonProps {
  children?: ReactNode;
  isSelected?: boolean;
  onClick: (value: string) => void;
  value: string;
}

export function GroupHeaderOption({
  children,
  isSelected,
  onClick,
  value
}: GroupHeaderOptionProps) {
  const handleClick = () => {
    onClick(value);
  };

  return (
    <Button
      variant={isSelected ? "contained" : "outlined"}
      color="primary"
      onClick={handleClick}
    >
      {children}
    </Button>
  );
}
