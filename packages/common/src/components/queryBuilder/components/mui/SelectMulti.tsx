import { Checkbox, Input, ListItemText, MenuItem, Select } from "@mui/material";
import { ValueSelectorProps } from "../../types";

const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;
const MenuProps = {
  PaperProps: {
    style: {
      maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
      width: 250,
    },
  },
};

interface SelectOption {
  value: string;
  label: string;
}

interface SelectMultiProps extends ValueSelectorProps {
  selectedValue: string[];
  values: SelectOption[];
  onChange: (value: string) => void;
  onDelete: (value: string) => void;
}

export function SelectMulti({ onChange, onDelete, selectedValue = [], values = [] }: SelectMultiProps) {
  return (
    <Select
      multiple
      value={selectedValue}
      input={<Input id="select-multiple-checkbox" />}
      displayEmpty
      renderValue={(selected) => {
        if (Array.isArray(selected) && selected.length > 0) {
          const items: string[] = [];

          selected.forEach((item) => {
            const value = values.find((value) => item === value.value);
            if (value) {
              items.push(value.label);
            }
          });

          return items.join(", ");
        }

        return "Select your option";
      }}
      MenuProps={MenuProps}
    >
      <MenuItem value="" disabled>
        Select your option
      </MenuItem>
      {values.map(({ value, label }) => {
        const isChecked =
          selectedValue.filter((item) => item === value).length !== 0;

        return (
          <MenuItem key={value} value={value}>
            <Checkbox
              checked={isChecked}
              onClick={() => {
                if (isChecked) {
                  onDelete(value);
                } else {
                  onChange(value);
                }
              }}
            />
            <ListItemText primary={label} />
          </MenuItem>
        );
      })}
    </Select>
  );
}
