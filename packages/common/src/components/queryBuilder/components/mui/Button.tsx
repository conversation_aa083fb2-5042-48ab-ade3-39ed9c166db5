import { Button as ButtonBase } from "@mui/material";
import styled from "styled-components";
import { ActionProps } from "../../types";

const StyledButton = styled(ButtonBase)`
  white-space: nowrap;
`;

export function Button({ label, handleOnClick: onClick }: ActionProps) {
  return (
    <StyledButton onClick={onClick} color="primary" variant="contained">
      {label}
    </StyledButton>
  );
}
