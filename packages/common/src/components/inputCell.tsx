import { CSSProperties, FC, useEffect, useState } from "react";
import { TextField } from "@mui/material";
import { useTableKeyboardNavigation } from "@progress/kendo-react-data-tools";

// Define the grid column index attribute constant
const GRID_COL_INDEX_ATTRIBUTE = "data-grid-col-index";

interface DataItem {
  id?: string | number;
  inEdit?: boolean;
  [key: string]: any;
}

interface ChangeEvent {
  dataIndex: number;
  dataItem: DataItem;
  field: string;
  value: string | number;
}

interface InputCellProps {
  dataItem: DataItem;
  field?: string;
  onChange?: (event: ChangeEvent) => void;
  dataIndex?: number;
  error?: string;
  alwaysEditable?: boolean;
  type?: string;
  sx?: any;
  style?: CSSProperties;
  debounceDelay?: number;
  min?: number;
  max?: number;
  step?: number;
  id?: string;
  colSpan?: number;
  ariaColumnIndex?: number;
  isSelected?: boolean;
  columnIndex?: number;
}

const InputCell: FC<InputCellProps> = (props) => {
  const {
    dataItem,
    field = "",
    onChange,
    dataIndex = 0,
    error,
    alwaysEditable = false,
    type = "text",
    debounceDelay = 0
  } = props;

  const [value, setValue] = useState<string | number>("");
  const [timer, setTimer] = useState<NodeJS.Timeout | null>(null);
  const navigationAttributes = useTableKeyboardNavigation(props.id || '');

  useEffect(() => {
    if (dataItem && field) {
      setValue(dataItem[field] !== undefined && dataItem[field] !== null ? dataItem[field] : "");
    }
  }, [dataItem, field]);

  if (!dataItem) {
    console.warn("InputCell received null or undefined dataItem");
    return null;
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = type === "number" ?
      (e.target.value === "" ? "" : Number(e.target.value)) :
      e.target.value;

    setValue(newValue);

    if (timer) {
      clearTimeout(timer);
    }

    if (onChange) {
      if (debounceDelay > 0) {
        const newTimer = setTimeout(() => {
          onChange({
            dataIndex,
            dataItem,
            field,
            value: newValue
          });
        }, debounceDelay);

        setTimer(newTimer);
      } else {
        onChange({
          dataIndex,
          dataItem,
          field,
          value: newValue
        });
      }
    }
  };

  return (
    <td
      colSpan={props.colSpan}
      role={"gridcell"}
      aria-colindex={props.ariaColumnIndex}
      aria-selected={props.isSelected}
      {...{
        [GRID_COL_INDEX_ATTRIBUTE]: props.columnIndex,
      }}
      {...navigationAttributes}
    >
      {(dataItem.inEdit || alwaysEditable) ? (
        <div style={{ position: 'relative' }}>
          <TextField
            type={type}
            value={value}
            onChange={handleChange}
            error={!!error}
            helperText={error}
            fullWidth
            size="small"
            inputProps={{
              min: props.min,
              max: props.max,
              step: props.step
            }}
            sx={props.sx}
            style={props.style}
          />
        </div>
      ) : (
        <span>{dataItem[field] !== undefined && dataItem[field] !== null ? dataItem[field] : ""}</span>
      )}
    </td>
  );
};

export default InputCell;
