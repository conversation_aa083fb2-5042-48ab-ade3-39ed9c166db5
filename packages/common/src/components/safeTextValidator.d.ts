import { FC, ReactNode } from "react";
interface TextValidatorProps {
    validators?: string[];
    errorMessages?: string[];
    value: any;
    onChange: (event: any) => void;
    children?: ReactNode;
    select?: boolean;
    [key: string]: any;
}
/**
 * Props for the SafeTextValidator component
 */
interface SafeTextValidatorProps extends TextValidatorProps {
    select?: boolean;
    children?: ReactNode;
}
/**
 * A safer wrapper around TextValidator that handles select property correctly
 * and prevents common errors with children and select properties
 */
declare const SafeTextValidator: FC<SafeTextValidatorProps>;
export default SafeTextValidator;
