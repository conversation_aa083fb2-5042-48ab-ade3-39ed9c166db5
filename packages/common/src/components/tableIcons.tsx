import { forwardRef } from "react";

// Create a simple placeholder icon component
const PlaceholderIcon = (props: any) => {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      {...props}
    >
      <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
    </svg>
  );
};

// Create a record of icon components
export const tableIcons = {
  Add: forwardRef((props: any, ref: any) => <PlaceholderIcon {...props} ref={ref} />),
  Check: forwardRef((props: any, ref: any) => <PlaceholderIcon {...props} ref={ref} />),
  Clear: forwardRef((props: any, ref: any) => <PlaceholderIcon {...props} ref={ref} />),
  Delete: forwardRef((props: any, ref: any) => <PlaceholderIcon {...props} ref={ref} />),
  DetailPanel: forwardRef((props: any, ref: any) => <PlaceholderIcon {...props} ref={ref} />),
  Edit: forwardRef((props: any, ref: any) => <PlaceholderIcon {...props} ref={ref} />),
  Export: forwardRef((props: any, ref: any) => <PlaceholderIcon {...props} ref={ref} />),
  Filter: forwardRef((props: any, ref: any) => <PlaceholderIcon {...props} ref={ref} />),
  FirstPage: forwardRef((props: any, ref: any) => <PlaceholderIcon {...props} ref={ref} />),
  LastPage: forwardRef((props: any, ref: any) => <PlaceholderIcon {...props} ref={ref} />),
  NextPage: forwardRef((props: any, ref: any) => <PlaceholderIcon {...props} ref={ref} />),
  PreviousPage: forwardRef((props: any, ref: any) => <PlaceholderIcon {...props} ref={ref} />),
  ResetSearch: forwardRef((props: any, ref: any) => <PlaceholderIcon {...props} ref={ref} />),
  Search: forwardRef((props: any, ref: any) => <PlaceholderIcon {...props} ref={ref} />),
  SortArrow: forwardRef((props: any, ref: any) => <PlaceholderIcon {...props} ref={ref} />),
  ThirdStateCheck: forwardRef((props: any, ref: any) => <PlaceholderIcon {...props} ref={ref} />),
  ViewColumn: forwardRef((props: any, ref: any) => <PlaceholderIcon {...props} ref={ref} />),
};
