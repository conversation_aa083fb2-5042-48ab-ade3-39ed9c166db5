{"name": "@pnmui/common", "version": "0.0.1", "private": true, "main": "./src/index.ts", "types": "./src/index.ts", "scripts": {"build": "tsc", "lint": "eslint \"src/**/*.{ts,tsx}\"", "clean": "<PERSON><PERSON><PERSON> dist"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@hello-pangea/dnd": "^18.0.1", "@mui/icons-material": "^7.0.1", "@mui/material": "^7.0.1", "@mui/x-data-grid": "^7.28.3", "@pnmui/common": "file:", "@progress/kendo-data-query": "^1.6.0", "@progress/kendo-drawing": "^1.17.5", "@progress/kendo-licensing": "^1.3.1", "@progress/kendo-react-animation": "^5.16.1", "@progress/kendo-react-buttons": "^5.16.1", "@progress/kendo-react-data-tools": "^5.16.1", "@progress/kendo-react-dateinputs": "^5.16.1", "@progress/kendo-react-dropdowns": "^5.16.1", "@progress/kendo-react-form": "^5.16.1", "@progress/kendo-react-grid": "^5.16.1", "@progress/kendo-react-inputs": "^5.16.1", "@progress/kendo-react-intl": "^5.16.1", "@progress/kendo-react-popup": "^5.16.1", "@progress/kendo-react-progressbars": "^5.16.1", "@progress/kendo-svg-icons": "^2.0.0", "@progress/kendo-theme-default": "^6.7.0", "@progress/kendo-theme-material": "^6.7.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^5.5.0", "react-router-dom": "^7.5.0", "react-toastify": "^11.0.5"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@types/react-router-dom": "^5.3.3", "eslint": "^8.55.0", "typescript": "^5.3.3"}, "peerDependencies": {"react": "^18.2.0", "react-dom": "^18.2.0"}}