import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Button,
  Card,
  CardActions,
  CardContent,
  CardHeader,
  MenuItem,
} from "@mui/material";
import { CommandCell, ErrorsDisplay, InputCell, SelectCell } from "@pnmui/common/components";
import { $i18n } from "@pnmui/common/services/propertiesService";
import { SmscEnumerationsService } from "@pnmui/common/services/smsc/enumerationsService";
import { SmscNumberListsService } from "@pnmui/common/services/smsc/numberListsService";
import { GridToolbar } from "@progress/kendo-react-grid";
import { ChangeEvent, useEffect, useState } from "react";
import {
  SelectValidator,
  TextValidator,
  ValidatorForm,
} from "react-material-ui-form-validator";
import { useNavigate, useParams } from "react-router-dom";
import { toast } from "react-toastify";
import { Subscription } from "rxjs";
import { tap } from "rxjs/operators";
import "../common.css";
import { Column, Grid } from "../components/KendoGridWrapper";
import { EnumerationOption } from "../types";

interface NumberRange {
  id?: string | number;
  from: string;
  to: string;
  typeOfNumber?: string;
  inEdit?: boolean;
}

interface NumberItem {
  id?: string | number;
  number: string;
  typeOfNumber?: string;
  inEdit?: boolean;
}

interface NumberPrefix {
  id?: string | number;
  prefix: string;
  typeOfNumber?: string;
  inEdit?: boolean;
}

interface NumberListFormModel {
  id?: string | number | null;
  name: string;
  numberListType: string;
  ranges: NumberRange[];
  numbers: NumberItem[];
  prefixes: NumberPrefix[];
  createdTime?: string;
  lastUpdatedTime?: string;
}

interface FormErrors {
  ranges?: Record<string | number, Record<string, string>>;
  numbers?: Record<string | number, Record<string, string>>;
  prefixes?: Record<string | number, Record<string, string>>;
}

const SmscNumberListForm = () => {
  const navigate = useNavigate();
  const params = useParams<{ id: string }>();
  const [i18n, setI18n] = useState<Record<string, string>>({});
  const [errorResponse, setErrorResponse] = useState<any>(null);
  const [numberList, setNumberList] = useState<NumberListFormModel>({
    id: null,
    name: "",
    numberListType: "",
    ranges: [],
    numbers: [],
    prefixes: [],
  });
  const [numberListTypeOptions, setNumberListTypeOptions] = useState<EnumerationOption[]>([]);
  const [typeOfNumberOptions, setTypeOfNumberOptions] = useState<EnumerationOption[]>([]);
  const [errors, setErrors] = useState<FormErrors>({});

  useEffect(() => {
    const subscription: Subscription = $i18n.pipe(tap()).subscribe((i18NProps) => {
      if (i18NProps) {
        setI18n(i18NProps);
      }
    });

    SmscEnumerationsService.getEnumerations()
      .then((response: Record<string, EnumerationOption[]>) => {
        const numberListType = response.numberListType || [];
        const typeOfNumber = response.typeOfNumber || [];
        setNumberListTypeOptions(numberListType);
        setTypeOfNumberOptions(typeOfNumber);
      })
      .catch((error: any) => {
        toast.error("Failed to fetch enumerations: " + error.message);
      });

    if (params.id && params.id !== "new") {
      SmscNumberListsService.getNumberListById(params.id)
        .then((data: NumberListFormModel) => {
          setNumberList({
            ...data,
            prefixes:
              data.numberListType === "PREFIX_LIST"
                ? data.numberEntries || []
                : [],
            numbers:
              data.numberListType === "WHOLE_NUMBER_LIST"
                ? data.numberEntries || []
                : [],
            ranges:
              data.numberListType === "WHOLE_NUMBER_RANGE_LIST"
                ? data.numberEntries || []
                : [],
          });
        })
        .catch((error: any) => {
          toast.error("Failed to load number list: " + error.message);
        });
    }

    return () => subscription.unsubscribe();
  }, [params.id]);

  const numberTypeCell = (props: any): JSX.Element | null => {
    if (!props.dataItem) {
      return null;
    }

    const value = props.dataItem[props.field];
    const selectedOption = typeOfNumberOptions.find(
      (option) => option.value === value
    );
    return (
      <SelectCell
        {...props}
        options={typeOfNumberOptions}
        value={selectedOption ? selectedOption.value : value}
        optionLabelField="value"
        style={{ width: "200px" }}
      />
    );
  };

  const generateUniqueId = (): number => {
    return Math.floor(Math.random() * 1000);
  };

  const handleChange = (e: ChangeEvent<HTMLInputElement>): void => {
    const { name, value } = e.target;
    setNumberList({ ...numberList, [name]: value });
  };

  const handleSubmit = (): void => {
    let numberEntries = [];
    let hasEntries = false;

    if (numberList.numberListType === "PREFIX_LIST") {
      // Ensure prefixes is an array and filter out null/undefined items
      const validPrefixes = Array.isArray(numberList.prefixes)
        ? numberList.prefixes.filter(prefix => prefix != null)
        : [];

      numberEntries = validPrefixes.map((prefix) => ({
        id: prefix.id || generateUniqueId(),
        numberType: prefix.numberType?.value || prefix.numberType || "",
        numberFrom: prefix.numberFrom || "",
        numberTo: prefix.numberTo || null,
      }));
      hasEntries = validPrefixes.length > 0;
    } else if (numberList.numberListType === "WHOLE_NUMBER_LIST") {
      // Ensure numbers is an array and filter out null/undefined items
      const validNumbers = Array.isArray(numberList.numbers)
        ? numberList.numbers.filter(number => number != null)
        : [];

      numberEntries = validNumbers.map((number) => ({
        id: number.id || generateUniqueId(),
        numberType: number.numberType?.value || number.numberType || "",
        numberFrom: number.numberFrom || "",
        numberTo: null,
      }));
      hasEntries = validNumbers.length > 0;
    } else if (numberList.numberListType === "WHOLE_NUMBER_RANGE_LIST") {
      // Ensure ranges is an array and filter out null/undefined items
      const validRanges = Array.isArray(numberList.ranges)
        ? numberList.ranges.filter(range => range != null)
        : [];

      numberEntries = validRanges.map((range) => ({
        id: range.id || generateUniqueId(),
        numberType: range.numberType?.value || range.numberType || "",
        numberFrom: range.numberFrom || "",
        numberTo: range.numberTo || "",
      }));
      hasEntries = validRanges.length > 0;
    }

    if (!hasEntries) {
      toast.error("At least one number entry is required");
      return;
    }

    const payload = {
      id: numberList.id || generateUniqueId(),
      name: numberList.name,
      numberListType: numberList.numberListType,
      numberEntries,
    };

    SmscNumberListsService.saveNumberList(payload)
      .then(() => {
        toast.success("Number List saved successfully");
        navigate("/smscNumberLists");
      })
      .catch((error: any) => {
        if (
          error.response &&
          error.response.data &&
          error.response.data.errors
        ) {
          setErrorResponse(error);
        } else {
          setErrorResponse({ message: "An unexpected error occurred." });
        }
      });
  };

  const addNew = (gridProp) => {
    const newDataItem = {
      id: generateUniqueId(),
      inEdit: true,
    };
    const currentData = Array.isArray(numberList[gridProp])
      ? numberList[gridProp]
      : [];
    setNumberList({
      ...numberList,
      [gridProp]: [newDataItem, ...currentData],
    });
  };

  const validateGridItem = (item, gridType) => {
    // Return empty errors if item is null or undefined
    if (!item) {
      return {};
    }

    const errors = {};

    console.log("item", item);

    // Common validation for number type
    if (!item.numberType) {
      errors.numberType = "Number type is required";
    }

    // Validate numbers to contain only digits
    const numberRegex = /^\d+$/;

    switch (gridType) {
      case "PREFIX_LIST":
        if (
          !item.numberFrom ||
          ((item.numberType?.value || item.numberType) !== "ALPHANUMERIC" &&
            !numberRegex.test(item.numberFrom))
        ) {
          errors.numberFrom = "Prefix must contain only digits";
        }
        break;

      case "WHOLE_NUMBER_LIST":
        if (
          !item.numberFrom ||
          ((item.numberType?.value || item.numberType) !== "ALPHANUMERIC" &&
            !numberRegex.test(item.numberFrom))
        ) {
          errors.numberFrom = "Number must contain only digits";
        }
        break;

      case "WHOLE_NUMBER_RANGE_LIST":
        if (
          !item.numberFrom ||
          ((item.numberType?.value || item.numberType) !== "ALPHANUMERIC" &&
            !numberRegex.test(item.numberFrom))
        ) {
          errors.numberFrom = "From number must contain only digits";
        }
        if (
          !item.numberTo ||
          ((item.numberType?.value || item.numberType) !== "ALPHANUMERIC" &&
            !numberRegex.test(item.numberFrom))
        ) {
          errors.numberTo = "To number must contain only digits";
        }
        if (
          (item.numberType?.value || item.numberType) !== "ALPHANUMERIC" &&
          parseInt(item.numberFrom) >= parseInt(item.numberTo)
        ) {
          errors.numberTo = "To number must be greater than From number";
        }
        break;
      default:
        // Handle unexpected gridType values
        errors.general = "Invalid grid type";
        break;
    }

    return errors;
  };

  const getErrorMessage = (dataItemId, field) => {
    return errors?.entries?.[dataItemId]?.[field];
  };

  const onItemChange = (event, gridProp) => {
    const { dataItem, field, value } = event;

    // Return early if dataItem is null or undefined
    if (!dataItem) {
      return;
    }

    let processedValue = value;

    // Handle number fields
    if (["numberFrom", "numberTo"].includes(field)) {
      if ((dataItem.numberType?.value || dataItem.numberType) !== "ALPHANUMERIC" && !value.match(/^\d*$/)) {
        return; // Don't update if not digits only
      }
    }

    // Ensure numberList[gridProp] is an array before mapping
    const currentData = Array.isArray(numberList[gridProp]) ? numberList[gridProp] : [];
    const updatedData = currentData.map((item) => {
      if (!item) return item;
      return (item.id && dataItem.id && item.id === dataItem.id) ? { ...item, [field]: processedValue } : item;
    });

    setNumberList({ ...numberList, [gridProp]: updatedData });
  };

  const renderGrid = (gridType, dataKey) => {
    let columns = [];
    if (gridType === "PREFIX_LIST") {
      columns = [
        {
          field: "numberType",
          title: "Number Type",
          cell: numberTypeCell,
        },
        {
          field: "numberFrom",
          title: "Prefix",
          cell: (props) => {
            if (!props.dataItem) {
              return null;
            }
            return (
              <InputCell
                {...props}
                onChange={(e) => onItemChange(e, dataKey)}
                error={getErrorMessage(props.dataItem.id, "numberFrom")}
                debounceDelay={500}
              />
            );
          },
        },
      ];
    } else if (gridType === "WHOLE_NUMBER_LIST") {
      columns = [
        {
          field: "numberType",
          title: "Number Type",
          cell: numberTypeCell,
        },
        {
          field: "numberFrom",
          title: "Number",
          cell: (props) => {
            if (!props.dataItem) {
              return null;
            }
            return (
              <InputCell
                {...props}
                onChange={(e) => onItemChange(e, dataKey)}
                error={getErrorMessage(props.dataItem.id, "numberFrom")}
                debounceDelay={500}
              />
            );
          },
        },
      ];
    } else if (gridType === "WHOLE_NUMBER_RANGE_LIST") {
      columns = [
        {
          field: "numberType",
          title: "Number Type",
          cell: numberTypeCell,
        },
        {
          field: "numberFrom",
          title: "Number From",
          cell: (props) => {
            if (!props.dataItem) {
              return null;
            }
            return (
              <InputCell
                {...props}
                onChange={(e) => onItemChange(e, dataKey)}
                error={getErrorMessage(props.dataItem.id, "numberFrom")}
                debounceDelay={500}
              />
            );
          },
        },
        {
          field: "numberTo",
          title: "Number To",
          cell: (props) => {
            if (!props.dataItem) {
              return null;
            }
            return (
              <InputCell
                {...props}
                onChange={(e) => onItemChange(e, dataKey)}
                error={getErrorMessage(props.dataItem.id, "numberTo")}
                debounceDelay={500}
              />
            );
          },
        },
      ];
    }

    return (
      <Grid
        data={numberList[dataKey]}
        editField="inEdit"
        onItemChange={(e) => onItemChange(e, dataKey)}
        style={{ marginTop: "2em" }}
      >
        <GridToolbar>
          <button
            type="button"
            className="k-primary k-button k-grid-edit-command"
            style={{ position: "absolute", right: "1em" }}
            onClick={() => addNew(dataKey)}
          >
            {i18n["button.add"] || "Add"}
          </button>
        </GridToolbar>

        {columns.map((col, index) => (
          <Column
            key={index}
            field={col.field}
            title={col.title}
            cell={col.cell}
          />
        ))}

        <Column
          cell={(props) => {
            if (!props.dataItem) {
              return null;
            }
            return (
              <CommandCell
                {...props}
                gridProp={dataKey}
                item={numberList}
                onChange={(data) =>
                  setNumberList({ ...numberList, [dataKey]: data })
                }
                onSave={(dataItem) => {
                  const errors = validateGridItem(
                    dataItem,
                    numberList.numberListType
                  );
                  if (Object.keys(errors).length > 0) {
                    setErrors((prev) => ({
                      ...prev,
                      entries: {
                        ...prev?.entries,
                        [dataItem.id]: errors,
                      },
                    }));
                    Object.values(errors).forEach((error) => {
                      toast.error(error);
                    });
                    return false;
                  }
                  return true;
                }}
              />
            );
          }}
          filterable={false}
        />
      </Grid>
    );
  };

  const goBack = (): void => {
    navigate("/smscNumberLists");
  };

  return (
    <div style={{ padding: "0.5em", paddingTop: "2em" }}>
      <ValidatorForm onSubmit={handleSubmit} className="tango-form">
        <Card>
          <CardContent>
            <ErrorsDisplay
              errorResponse={errorResponse}
              keyPrefix={"smsws.numberlist.form.keys"}
            />
            <div style={{ display: "flex", marginBottom: "1em" }}>
              <TextValidator
                label="Name"
                onChange={handleChange}
                name="name"
                value={numberList.name}
                validators={["required", "maxStringLength:50"]}
                errorMessages={[
                  "Name is required",
                  "Name cannot exceed 50 characters",
                ]}
                style={{ flex: 1, marginRight: "1em" }}
              />
              <SelectValidator
                label="Number List Type"
                name="numberListType"
                value={numberList.numberListType}
                onChange={handleChange}
                validators={["required"]}
                errorMessages={["Number list type is required"]}
                style={{ width: "200px" }}
              >
                {numberListTypeOptions.length ? (
                  numberListTypeOptions.map((type) => (
                    <MenuItem key={type.value} value={type.value}>
                      {type.value}
                    </MenuItem>
                  ))
                ) : (
                  <MenuItem value="">Loading...</MenuItem>
                )}
              </SelectValidator>
            </div>

            {numberList.numberListType === "PREFIX_LIST" &&
              renderGrid("PREFIX_LIST", "prefixes")}
            {numberList.numberListType === "WHOLE_NUMBER_LIST" &&
              renderGrid("WHOLE_NUMBER_LIST", "numbers")}
            {numberList.numberListType === "WHOLE_NUMBER_RANGE_LIST" &&
              renderGrid("WHOLE_NUMBER_RANGE_LIST", "ranges")}
          </CardContent>
          <CardActions className="card-actions content-card-actions">
            <Button variant="contained" color="secondary" onClick={goBack}>
              {i18n["button.cancel"] || "Cancel"}
            </Button>
            <Button variant="contained" color="primary" type="submit">
              {i18n["button.submit"] || "Submit"}
            </Button>
          </CardActions>
        </Card>
      </ValidatorForm>
    </div>
  );
};
export default SmscNumberListForm;



