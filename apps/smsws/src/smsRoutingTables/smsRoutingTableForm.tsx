import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Autocomplete,
  Button,
  Card,
  CardActions,
  CardContent,
  Paper,
  TextField,
  Typography,
} from "@mui/material";
import {
  <PERSON>mboBoxCell,
  CommandCell,
  ErrorsDisplay,
  InputCell,
} from "@pnmui/common/components";
import { $i18n } from "@pnmui/common/services/propertiesService";
import { SecurityService } from "@pnmui/common/services/securityService";
import { SmscRoutingClassesService } from "@pnmui/common/services/smsc/routingClassesService";
import { SmscSmsRoutingTablesService } from "@pnmui/common/services/smsc/smsRoutingTablesService";
import { GridItemChangeEvent, GridToolbar } from "@progress/kendo-react-grid";
import { ChangeEvent, useEffect, useState } from "react";
import { TextValidator, ValidatorForm } from "react-material-ui-form-validator";
import { useNavigate, useParams } from "react-router-dom";
import { toast } from "react-toastify";
import { Subscription } from "rxjs";
import { tap } from "rxjs/operators";
import "../common.css";
import "../stripAndReplace/stripAndReplaceFormDialog.css";
import { Column, Grid } from "../components/KendoGridWrapper";
import { RoutingClass } from "../types";

interface BNumberPrefixOverride {
  id?: string | number;
  bnumberPrefix: string;
  routingClass?: RoutingClass | null;
  inEdit?: boolean;
}

interface RoutingTableFormModel {
  id?: string | number;
  name: string;
  p2pDefaultRoutingClass: RoutingClass | null;
  p2aDefaultRoutingClass: RoutingClass | null;
  bnumberPrefixOverrides: BNumberPrefixOverride[];
  createdTime?: string;
  lastUpdatedTime?: string;
}

interface BNumberPrefixErrors {
  bnumberPrefix?: string;
  routingClass?: string;
}

interface FormErrors {
  bnumberPrefixOverrides: Record<string | number, BNumberPrefixErrors>;
}

const SmscRoutingTableForm = () => {
  const navigate = useNavigate();
  const [i18n, setI18n] = useState<Record<string, string>>({});
  const params = useParams<{ id: string }>();
  const [routingTable, setRoutingTable] = useState<RoutingTableFormModel>({
    id: 0,
    name: "",
    p2pDefaultRoutingClass: null,
    p2aDefaultRoutingClass: null,
    bnumberPrefixOverrides: [],
  });
  const [routingClasses, setRoutingClasses] = useState<RoutingClass[]>([]);
  const [errorResponse, setErrorResponse] = useState<any>(null);
  const [errors, setErrors] = useState<FormErrors>({
    bnumberPrefixOverrides: {}
  });
  const [touched, setTouched] = useState({
    p2pDefaultRoutingClass: false,
    p2aDefaultRoutingClass: false
  });
  const [formSubmitted, setFormSubmitted] = useState(false);
  const [p2pInputValue, setP2pInputValue] = useState('');
  const [p2aInputValue, setP2aInputValue] = useState('');

  useEffect(() => {
    const subscription: Subscription = $i18n.pipe(tap()).subscribe((i18NProps) => {
      if (i18NProps) {
        setI18n(i18NProps);
      }
    });

    SmscRoutingClassesService.getRoutingClasses().then((classes: RoutingClass[]) => {
      setRoutingClasses(classes);

      // Only after we have routing classes, get the routing table data
      if (params.id && params.id !== "new") {
        return SmscSmsRoutingTablesService.getSmsRoutingTableById(
          params.id
        ).then((data: any) => {
          const transformedData: RoutingTableFormModel = {
            ...data,
            bnumberPrefixOverrides: data.bnumberPrefixOverrides.map(
              (override: any) => {
                const routingClassObj = classes.find(
                  (rc) => rc.id === override.routingClass
                );
                return {
                  ...override,
                  prefixLength: override.bnumberLength,
                  inEdit: false,
                  routingClass: routingClassObj || override.routingClass,
                  bnumberLength: undefined,
                };
              }
            ),
          };
          setRoutingTable(transformedData);
        });
      }
    });

    return () => subscription.unsubscribe();
  }, [params.id]);

  const handleChange = (e: ChangeEvent<HTMLInputElement>): void => {
    setRoutingTable({ ...routingTable, [e.target.name]: e.target.value });
  };

  const handleRoutingClassChange = (name: string, value: RoutingClass | null): void => {
    setRoutingTable({ ...routingTable, [name]: value });
    setTouched(prev => ({ ...prev, [name]: true }));
  };

  const createNewRoutingClass = (routingClassName: string, fieldName: string): void => {
    if (!routingClassName) return;

    SmscRoutingClassesService.saveRoutingClassWithName(routingClassName)
      .then((data: RoutingClass) => {
        setRoutingClasses((prevClasses) => [...prevClasses, data]);
        handleRoutingClassChange(fieldName, data);

        // Clear the input value after creating a new routing class
        if (fieldName === 'p2pDefaultRoutingClass') {
          setP2pInputValue('');
        } else if (fieldName === 'p2aDefaultRoutingClass') {
          setP2aInputValue('');
        }

        toast.success(`New routing class "${routingClassName}" created successfully`);
      })
      .catch((error: any) => {
        console.error("Failed to create new routing class:", error);
        toast.error("Failed to create new routing class.");
      });
  };

  const addNew = (): void => {
    const newDataItem: BNumberPrefixOverride = {
      id: `new_${Date.now()}`,
      bnumberPrefix: "",
      inEdit: true,
    };
    setRoutingTable({
      ...routingTable,
      bnumberPrefixOverrides: [
        newDataItem,
        ...routingTable.bnumberPrefixOverrides,
      ],
    });
  };

  const onItemChange = (event: GridItemChangeEvent, gridProp: string): void => {
    const updatedData = (routingTable[gridProp as keyof RoutingTableFormModel] || []).map((item: BNumberPrefixOverride) =>
      (item.id && item.id === event.dataItem.id) ||
      (item.id && item.id === event.dataItem.tempId)
        ? { ...item, [event.field]: event.value }
        : item
    );
    setRoutingTable({ ...routingTable, [gridProp]: updatedData });
  };

  const handleSubmit = (): void => {
    setFormSubmitted(true);

    // Check if required fields are filled
    if (!routingTable.name || !routingTable.p2pDefaultRoutingClass || !routingTable.p2aDefaultRoutingClass) {
      toast.error("Please fill in all required fields");
      return;
    }

    const cleanedBnumberPrefixOverrides =
      routingTable.bnumberPrefixOverrides.map((override) => {
        const routingClass = override.routingClass?.id
          ? override.routingClass?.id
          : routingClasses.find(
              (c) =>
                c.name === override.routingClass?.name ||
                c.name === override.routingClass
            )?.id;
        return {
          ...override,
          routingClass,
          bnumberLength: override.prefixLength || null,
          tempId: undefined,
          inEdit: undefined,
          prefixLength: undefined,
        };
      });

    const payload = {
      ...routingTable,
      p2pDefaultRoutingClass:
        routingTable.p2pDefaultRoutingClass?.id ||
        routingTable.p2pDefaultRoutingClass,
      p2aDefaultRoutingClass:
        routingTable.p2aDefaultRoutingClass?.id ||
        routingTable.p2aDefaultRoutingClass,
      bnumberPrefixOverrides: cleanedBnumberPrefixOverrides,
    };

    SmscSmsRoutingTablesService.saveSmsRoutingTable(payload)
      .then(() => {
        toast.success("RoutingTable saved successfully");
        navigate("/smscRoutingTables");
      })
      .catch((error: any) => {
        console.error("handleSubmit error", error);
        setErrorResponse(error);

        if (error.response?.data?.errors) {
          const backendErrors = error.response.data.errors;
          toast.error(Object.values(backendErrors)[0]);
        } else {
          toast.error("Failed to save routingTable: " + error.message);
        }
      });
  };

  const goBack = (): void => {
    navigate(-1);
  };

  const getErrorMessage = (id: string | number | undefined, field: string): string | undefined => {
    if (!id) return undefined;
    return errors?.bnumberPrefixOverrides?.[id]?.[field];
  };

  const validateRoutingRule = (rule: BNumberPrefixOverride): Record<string, string> => {
    const errors: Record<string, string> = {};

    if (!rule.bnumberPrefix && rule.bnumberPrefix !== '0') {
      errors.bnumberPrefix = i18n["smsc.routingTableForm.error.prefixRequired"] || "Prefix is required";
    }

    if (rule.prefixLength && rule.prefixLength < 0) {
      errors.prefixLength = i18n["smsc.routingTableForm.error.prefixLengthPositive"] || "Prefix length must be a positive number";
    }

    if (!rule.routingClass) {
      errors.routingClass = i18n["smsc.routingTableForm.error.routingClassRequired"] || "Routing class is required";
    }

    return errors;
  };

  const renderGrid = (gridProp: string, routingTable: RoutingTableFormModel, onItemChange: (event: GridItemChangeEvent, gridProp: string) => void, addNew: () => void, i18n: Record<string, string>): JSX.Element => {
    return (
      <Grid
        data={routingTable[gridProp]}
        editField="inEdit"
        onItemChange={(e) => onItemChange(e, gridProp)}
        style={{ marginTop: "2em" }}
        editable={true}
      >
        <GridToolbar>
          <button
            type="button"
            className="k-primary k-button k-grid-edit-command"
            style={{ position: "absolute", right: "1em" }}
            onClick={addNew}
          >
            {i18n["button.add"] || "Add"}
          </button>
        </GridToolbar>
        <Column
          field="bnumberPrefix"
          title="B Number Prefix"
          editable={true}
          cell={(props) => (
            <InputCell
              {...props}
              type="text"
              error={getErrorMessage(props.dataItem.id || props.dataItem.tempId, 'bnumberPrefix')}
            />
          )}
        />
        <Column
          field="prefixLength"
          title="Exact B Number length"
          editable={true}
          cell={(props) => (
            <InputCell
              {...props}
              type="number"
              error={getErrorMessage(props.dataItem.id || props.dataItem.tempId, 'prefixLength')}
            />
          )}
        />
        <Column
          field="routingClass"
          title="Routing Class"
          editable={true}
          cell={(props) => (
            <ComboBoxCell
              {...props}
              onAddNew={(newValue) => {
                const routingClassName = typeof newValue === "string" ? newValue : newValue;
                SmscRoutingClassesService.saveRoutingClassWithName(routingClassName)
                  .then((data) => {
                    setRoutingClasses((prevClasses) => [...prevClasses, data]);
                    const updatedItems = routingTable[gridProp].map((item) =>
                      item.id === props.dataItem.id ? { ...item, routingClass: data } : item
                    );
                    setRoutingTable({
                      ...routingTable,
                      [gridProp]: updatedItems,
                    });
                  })
                  .catch((error) => {
                    console.error("Failed to create new routing class:", error);
                    toast.error("Failed to create new routing class.");
                  });
              }}
              options={routingClasses}
              value={props.dataItem.routingClass}
              textField="name"
              valueField="id"
              error={getErrorMessage(props.dataItem.id || props.dataItem.tempId, 'routingClass')}
            />
          )}
        />
        <Column
          cell={(props) => (
            <CommandCell
              {...props}
              item={{
                bnumberPrefixOverrides: routingTable.bnumberPrefixOverrides,
              }}
              onChange={(data) => {
                setRoutingTable({
                  ...routingTable,
                  bnumberPrefixOverrides: data,
                });
              }}
              onSave={(dataItem) => {
                const errors = validateRoutingRule(dataItem);
                if (Object.keys(errors).length > 0) {
                  setErrors(prev => ({
                    ...prev,
                    bnumberPrefixOverrides: {
                      ...prev.bnumberPrefixOverrides,
                      [dataItem.id || dataItem.tempId]: errors
                    }
                  }));
                  toast.error(Object.values(errors)[0]);
                  return false;
                }
                setErrors(prev => {
                  const newErrors = { ...prev };
                  if (newErrors.bnumberPrefixOverrides) {
                    delete newErrors.bnumberPrefixOverrides[dataItem.id || dataItem.tempId];
                  }
                  return newErrors;
                });
                return true;
              }}
              gridProp="bnumberPrefixOverrides"
            />
          )}
          filterable={false}
        />
      </Grid>
    );
  };

  return (
    <div style={{ padding: "0.5em", paddingTop: "2em" }}>
      <ValidatorForm onSubmit={handleSubmit} className="tango-form">
        <div style={{ marginLeft: "1em" }}>
          <Card>
            <CardContent>
              <ErrorsDisplay
                errorResponse={errorResponse}
                keyPrefix="smsws.routingTable.form.keys"
              />
              <TextValidator
                label="Name"
                onChange={handleChange}
                name="name"
                value={routingTable.name}
                validators={["required"]}
                errorMessages={["Name is required"]}
              />
              <Accordion
                style={{ marginTop: "1em", borderTop: "none" }}
                defaultExpanded={true}
              >
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Typography variant="h6" className="accordion-title">
                    {i18n["smsc.routingTablesForm.accordion.smsRoute"] ||
                      "SMS Route"}
                  </Typography>
                </AccordionSummary>
                <AccordionDetails>
                  <p>
                    {i18n[
                      "smsc.routingTablesForm.accordion.smsRoute.details"
                    ] ||
                      "We will first try to find a routing class by matching B Number Prefix. If there are no matches we will use the default for the message type."}
                  </p>
                  <div
                    className="flex-container"
                    style={{ justifyContent: "start" }}
                  >
                    <Autocomplete
                      id="p2pDefaultRoutingClass"
                      options={routingClasses}
                      getOptionLabel={(option) =>
                        typeof option === 'object' ? option.name :
                        routingClasses.find(c => c.id === option)?.name || ''
                      }
                      value={
                        typeof routingTable.p2pDefaultRoutingClass === 'object'
                          ? routingTable.p2pDefaultRoutingClass
                          : routingClasses.find(c => c.id === routingTable.p2pDefaultRoutingClass) || null
                      }
                      onChange={(_, newValue) => {
                        if (newValue && newValue.id) {
                          handleRoutingClassChange('p2pDefaultRoutingClass', newValue.id);
                        }
                      }}
                      isOptionEqualToValue={(option, value) => {
                        if (typeof value === 'number') {
                          return option.id === value;
                        }
                        return option.id === value?.id;
                      }}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          label="P2P Default Routing Class"
                          variant="outlined"
                          required
                          placeholder="Type to select or add a Routing Class"
                          error={(formSubmitted || touched.p2pDefaultRoutingClass) && !routingTable.p2pDefaultRoutingClass}
                          helperText={(formSubmitted || touched.p2pDefaultRoutingClass) && !routingTable.p2pDefaultRoutingClass ? "P2P Default Routing Class is required" : ""}
                          onBlur={() => setTouched(prev => ({ ...prev, p2pDefaultRoutingClass: true }))}
                        />
                      )}
                      style={{ width: '300px', marginRight: '16px' }}
                      inputValue={p2pInputValue}
                      onInputChange={(event, newInputValue) => setP2pInputValue(newInputValue)}
                      PaperComponent={(props) => (
                        <Paper {...props}>
                          {props.children}
                          <Button
                            disabled={
                              !p2pInputValue ||
                              routingClasses.some(c => c.name.toLowerCase() === p2pInputValue.toLowerCase())
                            }
                            color="primary"
                            fullWidth
                            sx={{ justifyContent: "flex-start", pl: 2 }}
                            onMouseDown={() => createNewRoutingClass(p2pInputValue, 'p2pDefaultRoutingClass')}
                          >
                            {i18n["smsc.application.createWithName.addNew"] || "+ Add New"}
                          </Button>
                        </Paper>
                      )}
                    />

                    <Autocomplete
                      id="p2aDefaultRoutingClass"
                      options={routingClasses}
                      getOptionLabel={(option) =>
                        typeof option === 'object' ? option.name :
                        routingClasses.find(c => c.id === option)?.name || ''
                      }
                      value={
                        typeof routingTable.p2aDefaultRoutingClass === 'object'
                          ? routingTable.p2aDefaultRoutingClass
                          : routingClasses.find(c => c.id === routingTable.p2aDefaultRoutingClass) || null
                      }
                      onChange={(_, newValue) => {
                        if (newValue && newValue.id) {
                          handleRoutingClassChange('p2aDefaultRoutingClass', newValue.id);
                        }
                      }}
                      isOptionEqualToValue={(option, value) => {
                        if (typeof value === 'number') {
                          return option.id === value;
                        }
                        return option.id === value?.id;
                      }}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          label="P2A Default Routing Class"
                          variant="outlined"
                          required
                          placeholder="Type to select or add a Routing Class"
                          error={(formSubmitted || touched.p2aDefaultRoutingClass) && !routingTable.p2aDefaultRoutingClass}
                          helperText={(formSubmitted || touched.p2aDefaultRoutingClass) && !routingTable.p2aDefaultRoutingClass ? "P2A Default Routing Class is required" : ""}
                          onBlur={() => setTouched(prev => ({ ...prev, p2aDefaultRoutingClass: true }))}
                        />
                      )}
                      style={{ width: '300px' }}
                      inputValue={p2aInputValue}
                      onInputChange={(event, newInputValue) => setP2aInputValue(newInputValue)}
                      PaperComponent={(props) => (
                        <Paper {...props}>
                          {props.children}
                          <Button
                            disabled={
                              !p2aInputValue ||
                              routingClasses.some(c => c.name.toLowerCase() === p2aInputValue.toLowerCase())
                            }
                            color="primary"
                            fullWidth
                            sx={{ justifyContent: "flex-start", pl: 2 }}
                            onMouseDown={() => createNewRoutingClass(p2aInputValue, 'p2aDefaultRoutingClass')}
                          >
                            {i18n["smsc.application.createWithName.addNew"] || "+ Add New"}
                          </Button>
                        </Paper>
                      )}
                    />
                  </div>
                  <div
                    className="flex-container"
                    style={{ justifyContent: "start" }}
                  >
                    <p>Default override list</p>
                    {renderGrid(
                      "bnumberPrefixOverrides",
                      routingTable,
                      onItemChange,
                      addNew,
                      i18n
                    )}
                  </div>
                </AccordionDetails>
              </Accordion>
            </CardContent>
            <CardActions className="card-actions content-card-actions">
                <Button
                  variant="contained"
                  color="secondary"
                  type="button"
                  onClick={goBack}
                >
                  {i18n["button.cancel"] || "Cancel"}
                </Button>

                {SecurityService.checkPermission(
                  "SMSC_ROUTING_TABLES_UPDATE_PERMISSION"
                ) && (
                  <Button
                    variant="contained"
                    color="primary"
                    type="submit"
                    className="request-handler-submit-button"
                  >
                    {i18n["button.submit"] || "Submit"}
                  </Button>
                )}
            </CardActions>
          </Card>
        </div>
      </ValidatorForm>
    </div>
  );
};

export default SmscRoutingTableForm;





