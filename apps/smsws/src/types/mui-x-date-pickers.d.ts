declare module '@mui/x-date-pickers' {
  import * as React from 'react';
  import { Dayjs } from 'dayjs';

  export interface DateTimePickerProps {
    value?: Dayjs | null;
    onChange?: (date: Dayjs | null) => void;
    label?: string;
    renderInput?: (params: any) => React.ReactNode;
    [key: string]: any;
  }

  export const DateTimePicker: React.ComponentType<DateTimePickerProps>;
}

declare module '@mui/x-date-pickers/AdapterDayjs' {
  import * as React from 'react';

  export class AdapterDayjs {
    constructor();
  }
}

declare module '@mui/x-date-pickers/LocalizationProvider' {
  import * as React from 'react';

  export interface LocalizationProviderProps {
    dateAdapter: any;
    children?: React.ReactNode;
    [key: string]: any;
  }

  export const LocalizationProvider: React.ComponentType<LocalizationProviderProps>;
}

declare module '@mui/x-date-pickers/TimePicker' {
  import * as React from 'react';
  import { Dayjs } from 'dayjs';

  export interface TimePickerProps {
    value?: Dayjs | null;
    onChange?: (date: Dayjs | null) => void;
    label?: string;
    renderInput?: (params: any) => React.ReactNode;
    [key: string]: any;
  }

  export const TimePicker: React.ComponentType<TimePickerProps>;
}
