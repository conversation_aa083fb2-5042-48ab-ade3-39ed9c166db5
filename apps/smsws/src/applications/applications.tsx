import { <PERSON><PERSON>, <PERSON>, CardContent, <PERSON>uI<PERSON>, TextField } from "@mui/material";
import { MaterialTable, tableIcons } from "@pnmui/common/components";
import { $i18n, $properties } from "@pnmui/common/services/propertiesService";
import { SecurityService } from "@pnmui/common/services/securityService";
import { SmscApplicationsService } from "@pnmui/common/services/smsc/applicationsService";
import { SmscConnectionsService } from "@pnmui/common/services/smsc/connectionsService";
import { SmscApplicationProfilesService } from "@pnmui/common/services/smsc/smppApplicationProfilesService";
import { paginate } from "@pnmui/common/utils";
import React, { ChangeEvent, useEffect, useState } from "react";
import { SelectValidator, ValidatorForm } from "react-material-ui-form-validator";
import { useNavigate } from "react-router-dom";
import { toast } from "react-toastify";
import { Subscription } from "rxjs";
import { tap } from "rxjs/operators";
import { Application } from "../types";

interface ApplicationsMap {
  [key: string]: string;
}

interface TableData {
  data: Application[];
  page: number;
  totalCount: number;
}

function  SmscApplications() {
  const [originalApplications, setOriginalApplications] = useState<
    Application[]
  >([]);
  const [applicationProfilesMap, setApplicationProfilesMap] =
    useState<ApplicationsMap>({});
  const [p2aConnectionsMap, setP2aConnectionsMap] = useState<ApplicationsMap>(
    {}
  );
  const [applications, setApplications] = useState<Application[]>([]);
  const [i18n, setI18n] = useState<Record<string, string>>({});
  const [selectedRows, setSelectedRows] = useState<(string | number)[]>([]);
  const [selectedName, setSelectedName] = useState<string>("");
  const [hostName, setHostName] = useState<string>("");
  const navigate = useNavigate();
  const tableRefApplications = React.createRef<any>();

  useEffect(() => {
    // Subscribe to i18n and properties changes
    const i18nSubscription: Subscription = $i18n
      .pipe(tap())
      .subscribe((i18NProps) => {
        if (i18NProps) {
          setI18n(i18NProps);
        }
      });

    // Subscribe to properties changes to ensure we have the correct base URL
    const propertiesSubscription: Subscription = $properties
      .pipe(tap())
      .subscribe((props: Record<string, string>) => {
        console.log("Applications component: Properties updated", props);
        console.log(
          "Applications component: client.smsws.base.url =",
          props["client.smsws.base.url"]
        );

        // Load data from real services
        populateApplications();
        populateApplicationProfiles();
        populateP2AConnections();
      });

    // Cleanup subscriptions on unmount
    return () => {
      i18nSubscription.unsubscribe();
      propertiesSubscription.unsubscribe();
    };
  }, []);

  // Real service functions to fetch data
  async function populateApplicationProfiles(): Promise<void> {
    try {
      const profilesResponse =
        await SmscApplicationProfilesService.getApplicationProfiles();
      const profilesList: Application[] = Array.isArray(profilesResponse)
        ? profilesResponse
        : [];
      const profilesMap: ApplicationsMap = {};
      profilesList.forEach((profile) => {
        if (profile.id !== undefined && profile.name) {
          profilesMap[profile.id] = profile.name;
        }
      });
      setApplicationProfilesMap(profilesMap);
    } catch (error) {
      console.error("Error fetching application profiles:", error);
      toast.error("Failed to fetch application profiles.");
    }
  }

  // Fetch P2A connections and create a mapping
  async function populateP2AConnections(): Promise<void> {
    try {
      const connectionsResponse = await SmscConnectionsService.getConnections();
      const connectionsList: Application[] = Array.isArray(connectionsResponse)
        ? connectionsResponse
        : [];
      const connectionsMap: ApplicationsMap = {};
      connectionsList.forEach((connection) => {
        if (connection.id !== undefined && connection.name) {
          connectionsMap[connection.id] = connection.name;
        }
      });
      setP2aConnectionsMap(connectionsMap);
    } catch (error) {
      console.error("Error fetching P2A connections:", error);
      toast.error("Failed to fetch P2A connections.");
    }
  }

  async function populateApplications(): Promise<void> {
    try {
      const applicationsResponse =
        await SmscApplicationsService.getApplications();
      const applicationsList: Application[] = Array.isArray(
        applicationsResponse
      )
        ? applicationsResponse
        : [];
      setApplications(applicationsList);
      setOriginalApplications(applicationsList);
    } catch (error) {
      console.error("Error fetching applications:", error);
      toast.error("Failed to fetch applications.");
    }
  }

  async function populateApplicationsData(query: {
    page: number;
    pageSize: number;
  }): Promise<TableData> {
    const paginatedList = paginate(applications, query.page, query.pageSize);
    return {
      data: paginatedList,
      page: query.page,
      totalCount: applications.length,
    };
  }

  const handleRowSelection = (rows: Application[]): void => {
    setSelectedRows(rows.map((row) => row.id || ""));
  };

  async function handleReload(event: React.FormEvent): Promise<void> {
    event.preventDefault();
    try {
      await SmscConnectionsService.loadFromFileSystem('applications', hostName);
      toast.success(
        i18n["smsc.applications.reloadSuccess"] ||
          "Applications reloaded successfully"
      );
      await populateApplications();
    } catch (error: any) {
      console.error("Error reloading applications:", error);
      toast.error(
        i18n["smsc.applications.reloadFailure"] ||
          "Failed to reload applications"
      );
    }
  }

  const handleDelete = async (): Promise<void> => {
    if (selectedRows.length === 0) {
      console.error("No rows selected for deletion.");
      return;
    }
    if (
      !SecurityService.checkPermission("SMSC_APPLICATION_DELETE_PERMISSION")
    ) {
      console.error("SMSC_APPLICATION_DELETE_PERMISSION is required.");
      return;
    }
    if (window.confirm("Are you sure you want to delete?")) {
      try {
        // Call the actual service to delete applications
        await Promise.all(
          selectedRows.map(async (id) => {
            return await SmscApplicationsService.deleteApplicationById(id);
          })
        );

        // Refresh the applications list after deletion
        await populateApplications();
        setSelectedRows([]);
        toast.success("Rows deleted successfully!");
      } catch (error: any) {
        console.error(error);
        toast.error(error.message);
      }
    }
  };

  useEffect(() => {
    tableRefApplications.current &&
      tableRefApplications.current.onQueryChange();
  }, [applications]);

  useEffect(() => {
    search();
  }, [selectedName]);

  function search(): void {
    if (selectedName === "" || selectedName.toLowerCase() === "any") {
      setApplications(originalApplications);
    } else {
      const filteredApplications = originalApplications.filter((application) =>
        application.name?.toLowerCase().includes(selectedName.toLowerCase())
      );
      setApplications(filteredApplications);
    }
  }

  return (
    <div className="wrapper">
      <Card className="content-card">
        <div className="form-row" style={{ display: "flex", alignItems: "flex-end", marginBottom: "1rem" }}>

          <div style={{ display: "flex", alignItems: "center" }}>
            <TextField
              label={i18n["smsc.resourcePolicies.hostName"] || "Host name"}
              variant="outlined"
              value={hostName}
              onChange={(e: ChangeEvent<HTMLInputElement>) =>
                setHostName(e.target.value)
              }
              style={{ marginRight: "1rem", minWidth: "15em" }}
              margin="normal"
            />

            <Button
              variant="contained"
              color="primary"
              onClick={handleReload}
              className="request-handler-reload-button"
              aria-label="Load from File"
              disabled={
                !SecurityService.checkPermission(
                  "SMSC_APPLICATION_READ_PERMISSION"
                ) || applications.length > 0
              }
              style={{
                display: applications.length > 0 ? 'none' : 'inline-flex',
                marginBottom: "8px"
              }}
            >
              {i18n["button.reload"] || "Load from File"}
            </Button>
          </div>

          <ValidatorForm onSubmit={() => {}} style={{ marginBottom: 0 }}>
            <SelectValidator
              name="applicationName"
              label={
                i18n["smsc.applications.applicationList.name"] || "Name"
              }
              margin="normal"
              variant="outlined"
              style={{
                minWidth: "15em",
                marginRight: "1rem",
              }}
              children={["Any", ...applications.map((c) => c.name)].map(
                (name) => (
                  <MenuItem key={name} value={name === "Any" ? "" : name}>
                    {name}
                  </MenuItem>
                )
              )}
              onChange={(e: any) => setSelectedName(e.target.value)}
              value={selectedName}
            />
          </ValidatorForm>
        </div>

        <CardContent>
          <MaterialTable
            tableRef={tableRefApplications}
            icons={tableIcons}
            data={populateApplicationsData}
            columns={[
              {
                field: "name",
                title: i18n["smsc.applications.applicationList.name"] || "Name",
              },
              {
                title: "Network Address Type",
                render: (rowData: any) => rowData.networkAddress?.type || "",
              },
              {
                title: "Network Address(es)",
                render: (rowData: any) => {
                  const addr = rowData.networkAddress;
                  if (!addr) return "";

                  switch (addr.type) {
                    case "Shortcode":
                      return addr.shortcode || "";
                    case "ShortcodeRange":
                      return `${addr.shortcodeFrom} - ${addr.shortcodeTo}`;
                    default:
                      return "";
                  }
                },
              },
              {
                title: "A2P Application Profile",
                render: (rowData: any) =>
                  rowData.applicationProfile
                    ? applicationProfilesMap[rowData.applicationProfile] ||
                      rowData.applicationProfile
                    : "",
              },
              {
                title: "P2A Connection",
                render: (rowData: any) =>
                  rowData.p2aConnection
                    ? p2aConnectionsMap[rowData.p2aConnection] ||
                      rowData.p2aConnection
                    : "",
              },
            ]}
            options={{
              selection: true,
              actionsColumnIndex: -1,
              toolbar: false,
              pageSize: 20,
              pageSizeOptions: [10, 20, 50],
              emptyRowsWhenPaging: false,
              headerStyle: { fontWeight: "bold" },
            }}
            onRowClick={(_event, rowData: any) => {
              navigate(`/smscApplicationForm/${rowData.id}`);
            }}
            onSelectionChange={(rows: any[]) => handleRowSelection(rows)}
          />
        </CardContent>
        <Button
          variant="contained"
          color="primary"
          className="request-handler-add-button"
          aria-label="Add"
          onClick={() => navigate("/smscApplicationForm/new")}
          style={{
            marginLeft: "15px",
            marginTop: "2rem",
            marginBottom: "1rem",
          }}
          disabled={
            !SecurityService.checkPermission(
              "SMSC_APPLICATION_CREATE_PERMISSION"
            )
          }
        >
          {i18n["button.add"] || "Add"}
        </Button>

        <span style={{ marginLeft: "10px" }}>
          <Button
            variant="contained"
            color="secondary"
            type="button"
            onClick={handleDelete}
            style={{ marginTop: "2rem", marginBottom: "1rem" }}
            className="request-handler-add-button"
            disabled={
              !SecurityService.checkPermission(
                "SMSC_APPLICATION_DELETE_PERMISSION"
              )
            }
          >
            {i18n["button.delete"] || "Delete"}
          </Button>
        </span>
      </Card>
    </div>
  );
}

export default SmscApplications;
