// Common types for the SMSWS application
// Strip and Replace types
export var StripAndReplaceType;
(function (StripAndReplaceType) {
    StripAndReplaceType["A_NUMBER"] = "A_NUMBER";
    StripAndReplaceType["B_NUMBER"] = "B_NUMBER";
})(StripAndReplaceType || (StripAndReplaceType = {}));
export var TypeOfNumber;
(function (TypeOfNumber) {
    TypeOfNumber["UNKNOWN"] = "UNKNOWN";
    TypeOfNumber["INTERNATIONAL"] = "INTERNATIONAL";
    TypeOfNumber["NATIONAL"] = "NATIONAL";
    TypeOfNumber["NETWORK_SPECIFIC"] = "NETWORK_SPECIFIC";
    TypeOfNumber["SUBSCRIBER_NUMBER"] = "SUBSCRIBER_NUMBER";
    TypeOfNumber["ALPHANUMERIC"] = "ALPHANUMERIC";
    TypeOfNumber["ABBREVIATED"] = "ABBREVIATED";
})(TypeOfNumber || (TypeOfNumber = {}));
export var NumberPlanIndicator;
(function (NumberPlanIndicator) {
    NumberPlanIndicator["UNKNOWN"] = "UNKNOWN";
    NumberPlanIndicator["ISDN"] = "ISDN";
    NumberPlanIndicator["DATA"] = "DATA";
    NumberPlanIndicator["TELEX"] = "TELEX";
    NumberPlanIndicator["LAND_MOBILE"] = "LAND_MOBILE";
    NumberPlanIndicator["NATIONAL"] = "NATIONAL";
    NumberPlanIndicator["PRIVATE"] = "PRIVATE";
    NumberPlanIndicator["ERMES"] = "ERMES";
    NumberPlanIndicator["INTERNET"] = "INTERNET";
    NumberPlanIndicator["WAP_CLIENT_ID"] = "WAP_CLIENT_ID";
})(NumberPlanIndicator || (NumberPlanIndicator = {}));
