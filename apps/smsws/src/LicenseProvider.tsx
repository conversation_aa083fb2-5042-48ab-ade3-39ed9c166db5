import React from 'react';
import { setScriptKey } from '@progress/kendo-licensing';

// Hard-coded license key
const KENDO_LICENSE_KEY = `-----BEGIN PROGRESS TELERIK LICENSE-----
eyJhbGciOiJSUzI1NiIsInR5cCI6IkxJQyJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.SIGNATURE_PLACEHOLDER
-----<PERSON>ND PROGRESS TELERIK LICENSE-----`;

// Register the license immediately
try {
  setScriptKey(KENDO_LICENSE_KEY);
  console.log('KendoReact license registered successfully on module load');
} catch (error) {
  console.error('Failed to register KendoReact license on module load:', error);
}

// This component will be used to wrap the application and provide the license
const LicenseProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  React.useEffect(() => {
    // Register the license again when the component mounts
    try {
      setScriptKey(KENDO_LICENSE_KEY);
      console.log('KendoReact license registered successfully in component');
    } catch (error) {
      console.error('Failed to register KendoReact license in component:', error);
    }
  }, []);

  return <>{children}</>;
};

export default LicenseProvider;
