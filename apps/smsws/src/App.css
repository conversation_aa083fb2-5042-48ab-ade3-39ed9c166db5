/* Global styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif;
  background-color: #f5f5f5;
}

#root {
  width: 100%;
  height: 100vh;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

/* Content styles */
.content-card,
.head-card {
  padding: 0.5em;
  margin: 0.5em;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  border: 1px solid rgba(0, 0, 0, 0.12);
}

.content-card [class^="MuiCardContent"] {
  padding: 0;
}

/* Card styling to match accordion pattern */
.MuiCard-root {
  background-color: white !important;
  border-radius: 4px !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24) !important;
  border: 1px solid rgba(0, 0, 0, 0.12) !important;
}

/* Remove gray background from card headers */
.MuiCardHeader-root {
  background-color: transparent !important;
  padding: 16px 16px 0 16px !important;
}

.MuiCardHeader-title {
  color: rgba(0, 0, 0, 0.87) !important;
  font-weight: 600 !important;
  font-size: 1.1rem !important;
}

/* Ensure card content has proper padding */
.MuiCardContent-root {
  padding: 16px !important;
}

.MuiCardContent-root:last-child {
  padding-bottom: 16px !important;
}

.card-actions {
  width: 98%;
  display: inline-block !important;
  padding: 8px 4px !important;
}

.card-actions button {
  float: right !important;
  margin: 4px !important;
}

.content-card-actions {
  margin-top: 1em;
  margin-bottom: 0;
}

/* Table styles */
.tango-form .table table [class^="MuiTableRow"]:last-child [class^="MuiTableCell"] {
  border-bottom: none;
}

.root {
  width: 100%;
  margin-top: 30px;
  overflow-x: auto;
}

.table {
  min-width: 650px;
  overflow-x: visible;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
}

.table-container {
  width: 96%;
  margin-left: 2%;
  display: inline-block;
}

.tableRow {
  cursor: pointer;
}

/* Form styles */
.selectEmpty {
  margin-top: 20px;
}

.formControl {
  margin: 20px;
  min-width: 120px;
}

.tango-form .form-text {
  padding: 2%;
}

.form-row {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 1em;
  padding: 0 1em;
}

/* Dialog styles */
.error-dialog h2 {
  font-weight: bold;
  text-transform: uppercase;
  font-size: 1em;
}

.error-dialog span {
  color: gray;
}

.error {
  color: red;
}

/* Material UI overrides */
.MuiCardActions-root .MuiButton-root {
  margin-left: 8px !important;
}

.MuiTableCell-root {
  padding-left: 16px !important;
  padding-right: 16px !important;
}

/* Layout styles */
.container {
  padding: 20px;
  width: 100%;
  max-width: 100%;
  height: 100%;
  overflow: auto;
}

.wrapper {
  width: 100%;
  padding: 0;
}

.wrapper h2 {
  margin: 0.5em 0;
  padding: 0 1em;
  font-size: 1.5em;
  font-weight: normal;
}

/* Responsive styles */
@media (max-width: 768px) {
  .container {
    padding: 10px;
  }

  .form-row {
    flex-direction: column;
  }
}
