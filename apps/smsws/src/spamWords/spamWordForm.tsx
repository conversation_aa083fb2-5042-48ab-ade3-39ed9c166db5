import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Button,
  Card,
  CardActions,
  CardContent,
  FormControlLabel,
  Switch,
  TextField,
  Typography,
} from "@mui/material";
import { CommandCell, InputCell } from "@pnmui/common";
import { ErrorsDisplay } from "@pnmui/common/components";
import { $i18n } from "@pnmui/common/services/propertiesService";
import { SecurityService } from "@pnmui/common/services/securityService";
import { SmscSpamWordsService } from "@pnmui/common/services/smsc/spamWordsService";
import { GridToolbar } from "@progress/kendo-react-grid";
import { Grid, Column } from "../components/KendoGridWrapper";
import { ChangeEvent, useEffect, useState } from "react";
import { TextValidator, ValidatorForm } from "react-material-ui-form-validator";
import { useNavigate, useParams } from "react-router-dom";
import { toast } from "react-toastify";
import { Subscription } from "rxjs";
import { tap } from "rxjs/operators";
import "../stripAndReplace/stripAndReplaceFormDialog.css";

interface SpamWord {
  id?: string | number;
  word: string;
  score: number;
  inEdit?: boolean;
}

interface SpamWordFormModel {
  id?: string | number;
  name: string;
  caseSensitive: boolean;
  countMultipleOccurences: boolean;
  spamWords: SpamWord[];
  createdTime?: string;
  lastUpdatedTime?: string;
}

interface GridState {
  skip: number;
  take: number;
}

interface FormErrors {
  [key: string]: string | FormErrors;
}

const SmscSpamWordForm = () => {
  const navigate = useNavigate();
  const params = useParams<{ id: string }>();
  const [i18n, setI18n] = useState<Record<string, string>>({});
  const [errorResponse, setErrorResponse] = useState<any>(null);
  const isEditMode = params.id && params.id !== "new";
  const [spamWord, setSpamWord] = useState<SpamWordFormModel>({
    name: "",
    caseSensitive: false,
    countMultipleOccurences: false,
    spamWords: [],
  });
  const [gridState, setGridState] = useState<GridState>({ skip: 0, take: 10 });
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [filteredSpamWords, setFilteredSpamWords] = useState<SpamWord[]>([]);
  const [errors, setErrors] = useState<FormErrors>({});

  useEffect(() => {
    const subscription: Subscription = $i18n.pipe(tap()).subscribe((i18NProps) => {
      if (i18NProps) {
        setI18n(i18NProps);
      }
    });

    if (isEditMode && params.id) {
      SmscSpamWordsService.getSpamWordById(params.id)
        .then((data: any) => {
          if (data.spamWords) {
            const processedSpamWords = data.spamWords.map((word: any) => ({
              ...word,
              spamWord: word.name,
              inEdit: false,
            }));
            setSpamWord({ ...data, spamWords: processedSpamWords });
            setFilteredSpamWords(processedSpamWords);
          } else {
            toast.error("Failed to load spam word data.");
          }
        })
        .catch((error: any) => {
          console.error("Error fetching spam word:", error);
          toast.error("Failed to load spam word data.");
        });
    } else {
      setFilteredSpamWords([]);
    }

    return () => subscription.unsubscribe();
  }, [params.id, isEditMode]);

  const handleChange = (e: ChangeEvent<HTMLInputElement>): void => {
    const { name, value, type, checked } = e.target;
    setSpamWord({ ...spamWord, [name]: type === "checkbox" ? checked : value });
  };

  const validateSpamWord = (word: SpamWord): Record<string, string> => {
    const errors: Record<string, string> = {};

    if (!word.spamWord?.trim()) {
      errors.spamWord = 'Spam Word is required';
    }

    if (word.severityRating === undefined || word.severityRating === null) {
      errors.severityRating = 'Severity Rating is required';
    } else if (!Number.isInteger(Number(word.severityRating)) || Number(word.severityRating) < 0) {
      errors.severityRating = 'Severity Rating must be a positive integer';
    }

    return errors;
  };

  const validateForm = (): boolean | Record<string, any> => {
    const newErrors = {};

    if (!spamWord.name?.trim()) {
      newErrors.name = 'Name is required';
    }

    if (spamWord.caseSensitive === undefined || spamWord.caseSensitive === null) {
      newErrors.caseSensitive = 'Case Sensitive is required';
    }

    if (spamWord.countMultipleOccurences === undefined || spamWord.countMultipleOccurences === null) {
      newErrors.countMultipleOccurences = 'Count Multiple Occurrences is required';
    }

    if (!spamWord.spamWords?.length) {
      newErrors.spamWords = 'At least one spam word is required';
      toast.error('At least one spam word is required');
      return false;
    }

    // Validate all spam words
    const spamWordErrors = {};
    spamWord.spamWords.forEach(word => {
      const wordErrors = validateSpamWord(word);
      if (Object.keys(wordErrors).length > 0) {
        spamWordErrors[word.id] = wordErrors;
      }
    });

    if (Object.keys(spamWordErrors).length > 0) {
      newErrors.spamWords = spamWordErrors;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (): void => {
    if (!validateForm()) {
      return;
    }

    const cleanedSpamWords = spamWord.spamWords.map((word) => ({
      id: word.id && word.id > 0 ? word.id : undefined,
      name: word.spamWord,
      severityRating: word.severityRating,
      token: word.spamWord,
    }));

    const payload = {
      ...spamWord,
      spamWords: cleanedSpamWords,
      caseSensitive: spamWord.caseSensitive === undefined ? false : spamWord.caseSensitive,
      countMultipleOccurences: spamWord.countMultipleOccurences === undefined ? false : spamWord.countMultipleOccurences
    };

    SmscSpamWordsService.saveSpamWordRegistry(payload)
      .then((response) => {
        const updatedSpamWords = response.spamWords.map((word, index) => ({
          ...word,
          id: response.spamWords[index].id,
        }));
        setSpamWord({ ...spamWord, spamWords: updatedSpamWords });
        toast.success("Spam Word saved successfully");
        navigate("/smscSpamWords");
      })
      .catch((error: any) => {
        setErrorResponse(error);
      });
  };

  const addNew = () => {
    const newDataItem = {
      id: undefined,
      inEdit: true,
      spamWord: "",
      severityRating: null,
    };
    const updatedSpamWords = [newDataItem, ...spamWord.spamWords];
    setSpamWord({ ...spamWord, spamWords: updatedSpamWords });
    applySearchAndPagination(updatedSpamWords, searchQuery, gridState);
  };

  const onItemChange = (event) => {
    const { dataItem, field, value } = event;
    let processedValue = value;

    // Convert severity rating to number
    if (field === 'severityRating') {
      processedValue = value === '' ? null : Number(value);
      if (isNaN(processedValue)) {
        return; // Don't update if not a valid number
      }
    }

    const updatedSpamWords = spamWord.spamWords.map((item) =>
      item === dataItem ? { ...item, [field]: processedValue } : item
    );
    setSpamWord({ ...spamWord, spamWords: updatedSpamWords });
    applySearchAndPagination(updatedSpamWords, searchQuery, gridState);
  };

  const applySearchAndPagination = (spamWordsData, query, gridState) => {
    let filteredData = spamWordsData;

    if (query) {
      filteredData = spamWordsData.filter((word) =>
        word.spamWord.toLowerCase().includes(query.toLowerCase())
      );
    }

    setFilteredSpamWords(filteredData);
  };

  const handleSearch = (e) => {
    const query = e.target.value;
    setSearchQuery(query);
    applySearchAndPagination(spamWord.spamWords, query, gridState);
  };

  const onPageChange = (event) => {
    setGridState({ skip: event.page.skip, take: event.page.take });
  };

  const updateSpamWords = (data) => {
    setSpamWord({ ...spamWord, spamWords: data });
    applySearchAndPagination(data, searchQuery, gridState);
  };

  const goBack = (): void => {
    navigate("/smscSpamWords");
  };

  const getErrorMessage = (dataItemId: string | number | undefined, field: string): string | undefined => {
    if (!dataItemId) return undefined;
    return errors?.spamWords?.[dataItemId]?.[field];
  };

  return (
    <ValidatorForm className="tango-form">
      <div style={{ marginLeft: "1em", paddingTop: "2em" }}>
        <Card>
          <CardContent>
            <ErrorsDisplay
              errorResponse={errorResponse}
              keyPrefix={"smsws.spamword.form.keys"}
            />
            <div
              style={{
                display: "flex",
                alignItems: "center",
                marginBottom: "1em",
              }}
            >
              <TextValidator
                label="Name"
                onChange={handleChange}
                name="name"
                value={spamWord.name}
                validators={["required"]}
                errorMessages={["Name is required"]}
              />
              <FormControlLabel
                control={
                  <Switch
                    checked={spamWord.caseSensitive}
                    onChange={handleChange}
                    name="caseSensitive"
                    color="primary"
                  />
                }
                label={
                  i18n[
                    "smsws.spamWordRegistry.spamWordRegistryForm.caseSensitiveCheckBox"
                  ] || "Case Sensitive"
                }
                style={{ marginLeft: "20px" }}
              />
              <FormControlLabel
                control={
                  <Switch
                    checked={spamWord.countMultipleOccurences}
                    onChange={handleChange}
                    name="countMultipleOccurences"
                    color="primary"
                  />
                }
                label={
                  i18n[
                    "smsws.spamWordRegistry.spamWordRegistryForm.countMultipleCheckBox"
                  ] || "Count Multiples"
                }
                style={{ marginLeft: "20px" }}
              />
            </div>
            <Accordion defaultExpanded style={{ marginTop: "1em" }}>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Typography variant="h6" className="accordion-title">
                  {i18n[
                    "smsws.spamWordRegistry.spamWordRegistryForm.spamWords.legend"
                  ] || "Spam words"}
                </Typography>
              </AccordionSummary>
              <AccordionDetails>
                <TextField
                  placeholder="Search spam words"
                  value={searchQuery}
                  onChange={handleSearch}
                  variant="outlined"
                  size="small"
                  style={{
                    marginBottom: "10px",
                    width: "260px",
                  }}
                />
                <div style={{ width: "100%" }}>
                  <Grid
                    data={filteredSpamWords.slice(
                      gridState.skip,
                      gridState.skip + gridState.take
                    )}
                    total={filteredSpamWords.length}
                    skip={gridState.skip}
                    take={gridState.take}
                    pageable
                    onPageChange={onPageChange}
                    editField="inEdit"
                    onItemChange={onItemChange}
                    style={{ width: "100%" }}
                  >
                    <GridToolbar>
                      <button
                        type="button"
                        className="k-primary k-button k-grid-edit-command"
                        style={{ position: "absolute", right: "1em" }}
                        onClick={addNew}
                      >
                        {i18n["button.add"] || "Add"}
                      </button>
                    </GridToolbar>
                    <Column
                      field="spamWord"
                      title={
                        i18n[
                          "smsws.spamWordRegistry.spamWordRegistryForm.popup.spamWord"
                        ] || "Spam Word"
                      }
                      cell={(props) => (
                        <InputCell
                          {...props}
                          onChange={onItemChange}
                          error={getErrorMessage(props.dataItem.id, 'spamWord')}
                          debounceDelay={500}
                        />
                      )}
                    />
                    <Column
                      field="severityRating"
                      title={
                        i18n[
                          "smsws.spamWordRegistry.spamWordRegistryForm.spamWordsTable.severityRating"
                        ] || "Severity Rating"
                      }
                      cell={(props) => (
                        <InputCell
                          {...props}
                          onChange={onItemChange}
                          error={getErrorMessage(props.dataItem.id, 'severityRating')}
                          debounceDelay={500}
                          type="number"
                          min={0}
                          step={1}
                        />
                      )}
                    />
                    <Column
                      cell={(props) => (
                        <CommandCell
                          {...props}
                          item={spamWord}
                          onChange={updateSpamWords}
                          onSave={(dataItem) => {
                            const errors = validateSpamWord(dataItem);
                            if (Object.keys(errors).length > 0) {
                              setErrors(prev => ({
                                ...prev,
                                spamWords: {
                                  ...prev?.spamWords,
                                  [dataItem.id]: errors
                                }
                              }));
                              Object.values(errors).forEach(error => {
                                toast.error(error);
                              });
                              return false;
                            }
                            return true;
                          }}
                          gridProp={"spamWords"}
                        />
                      )}
                      width="200px"
                    />
                  </Grid>
                </div>
              </AccordionDetails>
            </Accordion>
            </CardContent>
          <CardActions className="card-actions content-card-actions">
              <Button variant="contained" color="secondary" onClick={goBack}>
                {i18n["button.cancel"] || "Cancel"}
              </Button>
              {SecurityService.checkPermission(
                "SMSC_SPAM_REGISTRIES_UPDATE_PERMISSION"
              ) && (
                <Button
                  variant="contained"
                  color="primary"
                  type="submit"
                  className="request-handler-submit-button"
                  onClick={handleSubmit}
                >
                  {i18n["button.submit"] || "Submit"}
                </Button>
              )}
            </CardActions>
        </Card>
      </div>
    </ValidatorForm>
  );
};

export default SmscSpamWordForm;





