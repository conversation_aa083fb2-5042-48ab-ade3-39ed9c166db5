// Common types for the SMSWS application

// Application types
export interface Application {
  id?: string | number;
  name?: string;
  description?: string;
  type?: string;
  status?: string;
  createdAt?: string;
  updatedAt?: string;
  [key: string]: any;
}

// Connection types
export interface Connection {
  id?: string | number;
  name?: string;
  description?: string;
  type?: string;
  host?: string;
  port?: number;
  username?: string;
  password?: string;
  status?: string;
  createdAt?: string;
  updatedAt?: string;
  [key: string]: any;
}

// Delivery and Retry Profile types
export interface DeliveryAndRetryProfile {
  id?: string | number;
  name?: string;
  description?: string;
  maxRetries?: number;
  retryInterval?: number;
  expiryTime?: number;
  createdAt?: string;
  updatedAt?: string;
  [key: string]: any;
}

// Delivery and Retry Schedule types
export interface ScheduleException {
  id?: string | number;
  date?: string;
  description?: string;
  isHoliday?: boolean;
  [key: string]: any;
}

export interface ScheduleState {
  id?: string | number;
  dayOfWeek?: number;
  startTime?: string;
  endTime?: string;
  isActive?: boolean;
  [key: string]: any;
}

export interface ErrorSchedule {
  id?: string | number;
  mapErrorCode?: string;
  retryCount?: number;
  retryInterval?: number;
  inEdit?: boolean;
  [key: string]: any;
}

export interface StateErrors {
  dayOfWeek?: string;
  startTime?: string;
  endTime?: string;
  isActive?: string;
  [key: string]: any;
}

export interface StateExceptions {
  date?: string;
  description?: string;
  isHoliday?: string;
  [key: string]: any;
}

export interface DeliveryAndRetrySchedule {
  id?: string | number;
  name?: string;
  description?: string;
  exceptions?: ScheduleException[];
  states?: ScheduleState[];
  errorSchedules?: ErrorSchedule[];
  createdAt?: string;
  updatedAt?: string;
  [key: string]: any;
}

// IP List types
export interface IPAddress {
  id?: string | number;
  address?: string;
  description?: string;
  [key: string]: any;
}

export interface IPList {
  id?: string | number;
  name?: string;
  description?: string;
  addresses?: IPAddress[];
  createdAt?: string;
  updatedAt?: string;
  [key: string]: any;
}

// Number List types
export interface PhoneNumber {
  id?: string | number;
  number?: string;
  description?: string;
  [key: string]: any;
}

export interface NumberList {
  id?: string | number;
  name?: string;
  description?: string;
  numbers?: PhoneNumber[];
  createdAt?: string;
  updatedAt?: string;
  [key: string]: any;
}

// Resource Policy types
export interface ResourcePolicy {
  id?: string | number;
  name?: string;
  description?: string;
  maxConnections?: number;
  maxMessagesPerSecond?: number;
  createdAt?: string;
  updatedAt?: string;
  [key: string]: any;
}

// Routing Class types
export interface RoutingClass {
  id?: string | number;
  name?: string;
  description?: string;
  priority?: number;
  createdAt?: string;
  updatedAt?: string;
  [key: string]: any;
}

// SMPP Application Profile types
export interface BlackoutPeriod {
  id?: string | number;
  startTime?: string;
  endTime?: string;
  dayOfWeek?: string;
  weekdays?: string[];
  daysOfWeek?: number[];
  inEdit?: boolean;
  [key: string]: any;
}

export interface AdvancedProperty {
  id: string | number;
  section?: string;
  name?: string;
  value?: string;
  inEdit?: boolean;
  [key: string]: any;
}

export interface AdvancedSettings {
  enabled?: boolean;
  properties?: AdvancedProperty[];
  [key: string]: any;
}

export interface BlackoutSettings {
  enabled?: boolean;
  periods?: BlackoutPeriod[];
  [key: string]: any;
}

export interface CDRSettings {
  enabled?: boolean;
  generateCDRsForDeliveryReceipts?: boolean;
  generateCDRsForSubmitSMResponses?: boolean;
  generateCDRsForIncomingMessages?: boolean;
  [key: string]: any;
}

export interface ChargingSettings {
  enabled?: boolean;
  chargingType?: string;
  chargingDestination?: string;
  chargingOrigin?: string;
  chargingAmount?: string;
  [key: string]: any;
}

export interface ContentScreeningSettings {
  enabled?: boolean;
  spamWordRegistry?: string | number | SpamWordRegistry;
  spamWordThreshold?: number;
  rejectOnThresholdExceeded?: boolean;
  [key: string]: any;
}

export interface EncodingSettings {
  defaultAlphabetForReceivedSMS?: string;
  defaultAlphabetForDeliveryOfSMS?: string;
  [key: string]: any;
}

export interface NumberScreeningSettings {
  enabled?: boolean;
  originNumberList?: string | number | NumberList;
  destinationNumberList?: string | number | NumberList;
  rejectOnOriginMatch?: boolean;
  rejectOnDestinationMatch?: boolean;
  [key: string]: any;
}

export interface ReceiptSettings {
  enabled?: boolean;
  receiptDeliveryAndRetryProfile?: string | number | DeliveryAndRetryProfile;
  receiptResourcePolicy?: string | number | ResourcePolicy;
  receiptStoragePolicy?: string;
  receiptStorageDuration?: number;
  receiptStorageDurationUnit?: string;
  [key: string]: any;
}

export interface RetriesSettings {
  enabled?: boolean;
  temporaryErrorRetryProfile?: string | number | DeliveryAndRetryProfile;
  permanentErrorRetryProfile?: string | number | DeliveryAndRetryProfile;
  [key: string]: any;
}

export interface StorageSettings {
  enabled?: boolean;
  storagePolicy?: string;
  storageDuration?: number;
  storageDurationUnit?: string;
  resourcePolicy?: string | number | ResourcePolicy;
  [key: string]: any;
}

export interface ThrottlingSettings {
  enabled?: boolean;
  maxMessagesPerSecond?: number;
  maxMessagesPerMinute?: number;
  maxMessagesPerHour?: number;
  maxMessagesPerDay?: number;
  [key: string]: any;
}

export interface ApplicationProfile {
  id?: string | number;
  name?: string;
  description?: string;
  systemId?: string;
  password?: string;
  ipList?: string | IPList;
  maxBinds?: number;
  advanced?: AdvancedSettings;
  blackouts?: BlackoutSettings;
  cdrs?: CDRSettings;
  charging?: ChargingSettings;
  contentScreening?: ContentScreeningSettings;
  encoding?: EncodingSettings;
  numberScreening?: NumberScreeningSettings;
  receipts?: ReceiptSettings;
  retries?: RetriesSettings;
  storage?: StorageSettings;
  throttling?: ThrottlingSettings;
  createdAt?: string;
  updatedAt?: string;
  [key: string]: any;
}

// SMS Routing Table types
export interface RoutingRule {
  id?: string | number;
  priority?: number;
  sourceAddress?: string;
  destinationAddress?: string;
  connectionId?: string | number;
  routingClassId?: string | number;
  [key: string]: any;
}

export interface BNumberPrefixOverride {
  id?: string | number;
  bnumberPrefix?: string;
  prefixLength?: number;
  connectionId?: string | number;
  routingClassId?: string | number;
  inEdit?: boolean;
  [key: string]: any;
}

export interface RoutingTableFormModel {
  id?: string | number;
  name?: string;
  description?: string;
  rules?: RoutingRule[];
  bnumberPrefixOverrides?: BNumberPrefixOverride[];
  createdAt?: string;
  updatedAt?: string;
  [key: string]: any;
}

export interface BNumberPrefixErrors {
  bnumberPrefix?: string;
  prefixLength?: string;
  connectionId?: string;
  routingClassId?: string;
  [key: string]: any;
}

export interface SMSRoutingTable {
  id?: string | number;
  name?: string;
  description?: string;
  rules?: RoutingRule[];
  bnumberPrefixOverrides?: BNumberPrefixOverride[];
  createdAt?: string;
  updatedAt?: string;
  [key: string]: any;
}

// Spam Word types
export interface SpamWord {
  id?: string | number;
  word?: string;
  spamWord?: string;
  score?: number;
  severityRating?: number;
  description?: string;
  createdAt?: string;
  updatedAt?: string;
  inEdit?: boolean;
  [key: string]: any;
}

export interface SpamWordRegistry {
  id?: string | number;
  name?: string;
  description?: string;
  spamWords?: SpamWord[];
  caseSensitive?: boolean;
  countMultipleOccurences?: boolean;
  createdAt?: string;
  updatedAt?: string;
  [key: string]: any;
}

// Form types
export interface FormProps {
  initialValues?: any;
  onSubmit?: (values: any) => void;
  onCancel?: () => void;
  isEdit?: boolean;
}

// List types
export interface ListProps {
  onEdit?: (item: any) => void;
  onDelete?: (item: any) => void;
  onAdd?: () => void;
  data?: any[];
}

// Strip and Replace types
export enum StripAndReplaceType {
  A_NUMBER = 'A_NUMBER',
  B_NUMBER = 'B_NUMBER'
}

export enum TypeOfNumber {
  UNKNOWN = 'UNKNOWN',
  INTERNATIONAL = 'INTERNATIONAL',
  NATIONAL = 'NATIONAL',
  NETWORK_SPECIFIC = 'NETWORK_SPECIFIC',
  SUBSCRIBER_NUMBER = 'SUBSCRIBER_NUMBER',
  ALPHANUMERIC = 'ALPHANUMERIC',
  ABBREVIATED = 'ABBREVIATED'
}

export enum NumberPlanIndicator {
  UNKNOWN = 'UNKNOWN',
  ISDN = 'ISDN',
  DATA = 'DATA',
  TELEX = 'TELEX',
  LAND_MOBILE = 'LAND_MOBILE',
  NATIONAL = 'NATIONAL',
  PRIVATE = 'PRIVATE',
  ERMES = 'ERMES',
  INTERNET = 'INTERNET',
  WAP_CLIENT_ID = 'WAP_CLIENT_ID'
}

export interface StripAndReplaceEntry {
  id?: string | number;
  minDigits?: number;
  maxDigits?: number;
  stripTypeOfNumber?: TypeOfNumber;
  stripNumberPlanIndicator?: NumberPlanIndicator;
  stripNumber?: string;
  replaceTypeOfNumber?: TypeOfNumber;
  replaceNumberPlanIndicator?: NumberPlanIndicator;
  replaceNumber?: string;
  inEdit?: boolean;
  [key: string]: any;
}

export interface StripAndReplace {
  id?: string | number;
  moStripAndReplaceEnabled?: boolean;
  aoStripAndReplaceEnabled?: boolean;
  type?: StripAndReplaceType;
  stripAndReplaceEntries?: StripAndReplaceEntry[];
  createdAt?: string;
  updatedAt?: string;
  [key: string]: any;
}

// Number Analysis types
/**
 * Represents an A-Number white list entry in the Number Analysis configuration
 */
export interface NumberAnalysisANumberWhiteList {
  id?: string | number;
  /** The allow list reference (UI property) */
  allowList: string | number | NumberList;
  /** The anumber white list reference (server property) */
  anumberWhiteList?: string | number | NumberList;
  /** The charging method to apply */
  chargingMethod: string;
  /** The prepaid list reference */
  prepaidList: string | number | NumberList;
  /** Whether charging override is enabled for this entry */
  chargingOverrideEnabled: boolean;
  /** UI state property for editing */
  inEdit?: boolean;
}

/**
 * Represents a charging override entry in the Number Analysis configuration
 */
export interface NumberAnalysisChargingOverride {
  id?: string | number;
  /** The B-Number prefix for this override */
  bnumberPrefix: string;
  /** The charging method to apply */
  chargingMethod: string;
  /** The prepaid list reference */
  prepaidList: string | number | NumberList;
  /** UI state property for editing */
  inEdit?: boolean;
}

/**
 * Represents the Number Analysis configuration for MO (P2A, P2P) & AO (A2P) number handling
 */
export interface NumberAnalysis {
  id?: string | number;
  /** Required host for reloading charging methods */
  chargingMethodReloadHost: string;
  /** Optional A-Number block list reference */
  anumberBlackList?: string | number | NumberList;
  /** Optional B-Number block list reference */
  bnumberBlackList?: string | number | NumberList;
  /** Optional B-Number white list reference */
  bnumberWhiteList?: string | number | NumberList;
  /** List of A-Number white list entries */
  anumberWhiteLists: NumberAnalysisANumberWhiteList[];
  /** List of charging override entries */
  chargingOverrideList: NumberAnalysisChargingOverride[];
  /** Creation timestamp */
  createdTime?: string;
  /** Last update timestamp */
  lastUpdatedTime?: string;
}

// Common types
export interface SelectOption {
  value: string | number;
  label: string;
}

export interface EnumerationOption {
  value: string;
  displayText: string;
}

/**
 * Represents a charging method configuration
 */
export interface ChargingMethod {
  id?: string | number;
  /** The name of the charging method */
  name: string;
  /** Optional reference to the A-Number prepaid list */
  anumberPrepaidList?: string | number | NumberList;
}

export interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}
