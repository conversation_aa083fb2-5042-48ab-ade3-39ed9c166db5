import { ErrorsDisplay } from "@pnmui/common/components";
import { SecurityService } from "@pnmui/common/services/securityService";
import { ChangeEvent, useEffect, useState } from "react";
import { TextValidator, ValidatorForm } from "react-material-ui-form-validator";

import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Button,
  Card,
  CardActions,
  CardContent,
  CardHeader,
} from "@mui/material";
import { $i18n } from "@pnmui/common/services/propertiesService";
import { SmscDeliveryAndRetrySchedulesService } from "@pnmui/common/services/smsc/deliveryAndRetrySchedulesService";
import { SmscEnumerationsService } from "@pnmui/common/services/smsc/enumerationsService";
import { GridItemChangeEvent } from "@progress/kendo-react-grid";
import { useNavigate, useParams } from "react-router-dom";
import { toast } from "react-toastify";
import { Subscription } from "rxjs";
import { tap } from "rxjs/operators";
import "../common.css";
import { DeliveryAndRetrySchedule, EnumerationOption } from "../types";
import { ScheduleExceptionsGrid } from "./ScheduleExceptionsGrid";
import { ScheduleStatesGrid } from "./ScheduleStatesGrid";

interface ScheduleState {
  id?: string | number;
  name: string;
  sriRetryPriorityEnabled?: boolean;
  action?: string | EnumerationOption;
  delayAfterFailure?: number;
  nextState?: string | { id: string | number; name: string };
  inEdit?: boolean;
  stateExceptions?: StateException[];
}

interface StateException {
  id: string | number;
  stateToApplyException?: string | { id: string | number; name: string };
  mapErrorCode?: string | EnumerationOption;
  action?: string | EnumerationOption;
  delayAfterFailure?: number;
  nextState?: string | { id: string | number; name: string };
  inEdit?: boolean;
}

interface Enumerations {
  mapErrorCode?: EnumerationOption[];
  deliveryAndRetryScheduleStateAction?: EnumerationOption[];
  [key: string]: EnumerationOption[] | undefined;
}

interface StateErrors {
  name?: string;
  action?: string;
  delayAfterFailure?: string;
  nextState?: string;
}

interface StateExceptionErrors {
  stateToApplyException?: string;
  mapErrorCode?: string;
  action?: string;
  delayAfterFailure?: string;
  nextState?: string;
}

interface FormErrors {
  states: {
    [key: string]: StateErrors;
  };
  exceptions: {
    [key: string]: StateExceptionErrors;
  };
}

export default function SmscDeliveryAndRetryScheduleForm() {
  const navigate = useNavigate();
  const [i18n, setI18n] = useState<Record<string, string>>({});
  const params = useParams<{ id: string }>();
  const [errorResponse, setErrorResponse] = useState<any>(null);
  const [enumerations, setEnumerations] = useState<Enumerations>({});
  const [retrySchedule, setRetrySchedule] = useState<DeliveryAndRetrySchedule>({
    name: "",
    states: [
      {
        name: "Initial Delivery Attempt",
        stateExceptions: [],
        action: "CONTINUE",
        delayAfterFailure: 5,
        nextState: "Initial Delivery Attempt",
      },
    ],
  });
  const [allStateExceptions, setAllStateExceptions] = useState<
    StateException[]
  >([]);
  const [errors, setErrors] = useState<FormErrors>({
    states: {},
    exceptions: {},
  });

  useEffect(() => {
    const subscription: Subscription = $i18n
      .pipe(tap())
      .subscribe((i18NProps) => {
        if (i18NProps) {
          setI18n(i18NProps);
        }
      });
    SmscEnumerationsService.getEnumerations().then(setEnumerations);
    if (params.id && params.id !== "new") {
      SmscDeliveryAndRetrySchedulesService.getRetryScheduleById(params.id).then(
        (data: DeliveryAndRetrySchedule) => {
          console.log("RetrySchedule data", data);
          setRetrySchedule(data);
          let allExceptions: StateException[] = [];
          for (const state of data.states) {
            allExceptions = [
              ...allExceptions,
              ...state.stateExceptions.map((e: StateException) => ({
                ...e,
                stateToApplyException: state,
              })),
            ];
          }
          setAllStateExceptions(allExceptions);
        }
      );
    }

    return () => subscription.unsubscribe();
  }, [params.id]);

  useEffect(
    (prev) => {
      console.log("retrySchedule changed", retrySchedule);
      // setRetrySchedule({
      //   ...retrySchedule,
      //   states: retrySchedule.states
      //     .map((s, it) => ({
      //       name: s.nextState,
      //       id: retrySchedule.states.length + (it + 12345),
      //     }))
      //     .filter((s) => s.name),
      // });
    },
    [retrySchedule.states]
  );

  useEffect(() => {
    console.log("allStateExceptions changed", allStateExceptions);
    if (
      allStateExceptions.find(
        (exception) =>
          (exception.nextState && !exception.nextState?.name) ||
          (exception.stateToApplyException &&
            !exception.stateToApplyException?.name)
      )
    ) {
      const allStateExceptionsVar = [];
      allStateExceptions.forEach((exception) => {
        const stateToApplyException = retrySchedule.states.find(
          (st) =>
            (exception.stateToApplyException &&
              st.id === exception.stateToApplyException) ||
            (exception.stateToApplyException &&
              st.name === exception.stateToApplyException) ||
            (exception.stateToApplyException?.id &&
              st.id === exception.stateToApplyException?.id) ||
            (exception.stateToApplyException?.name &&
              st.name === exception.stateToApplyException?.name)
        );
        const nextState = retrySchedule.states.find(
          (st) =>
            exception.nextState &&
            (st.id === exception.nextState ||
              st.name === exception.nextState ||
              st.id === exception.nextState?.id)
        );
        allStateExceptionsVar.push({
          ...exception,
          nextState,
          stateToApplyException,
        });
      });
      console.log("allScheduleExceptions", allStateExceptionsVar);
      setAllStateExceptions(allStateExceptionsVar);
    }
  }, [allStateExceptions]);

  const handleChange = (
    e: ChangeEvent<HTMLInputElement | { name?: string; value: unknown }>
  ) => {
    if (!e.target.name) return;

    if (e.target.name.indexOf(".") > -1) {
      const [parent, child] = e.target.name.split(".");
      setRetrySchedule({
        ...retrySchedule,
        [parent]: {
          ...retrySchedule[parent as keyof DeliveryAndRetrySchedule],
          [child]: e.target.value,
        },
      });
    } else {
      setRetrySchedule({
        ...retrySchedule,
        [e.target.name]: e.target.value,
      });
    }
  };

  const getErrorMessage = (
    itemId: string | number | undefined,
    field: string,
    type: "states" | "exceptions" = "states"
  ): string | undefined => {
    if (!itemId) return undefined;
    return errors?.[type]?.[itemId]?.[field];
  };

  const handleSubmit = (): void => {
    let hasErrors = false;
    const newErrors: FormErrors = { states: {}, exceptions: {} };

    // Validate states
    retrySchedule.states.forEach((state) => {
      const validationErrors = validateState(state);
      if (Object.keys(validationErrors).length > 0) {
        hasErrors = true;
        if (state.id) {
          newErrors.states[state.id] = validationErrors;
        }
        const firstError = Object.values(validationErrors)[0];
        if (firstError) toast.error(firstError);
      }
    });

    // Validate exceptions
    allStateExceptions.forEach((exception) => {
      const validationErrors = validateStateException(exception);
      if (Object.keys(validationErrors).length > 0) {
        hasErrors = true;
        newErrors.exceptions[exception.id] = validationErrors;
        const firstError = Object.values(validationErrors)[0];
        if (firstError) toast.error(firstError);
      }
    });

    if (hasErrors) {
      setErrors(newErrors);
      return;
    }

    // Proceed with save if no errors
    SmscDeliveryAndRetrySchedulesService.saveRetrySchedule(retrySchedule)
      .then((data) => {
        toast.success(
          i18n["smsc.deliveryAndRetryScheduleForm.SaveSuccess"] ||
            "Schedule saved successfully"
        );
        navigate("/smscDeliveryAndRetrySchedules");
      })
      .catch((error: any) => {
        console.error("Save error:", error);
        setErrorResponse(error);
        toast.error(
          i18n["smsc.deliveryAndRetryScheduleForm.SaveFailure"] ||
            "Failed to save schedule"
        );
      });
  };

  function goBack() {
    navigate(-1);
  }

  const addNewState = (): void => {
    const newDataItem: ScheduleState = {
      id: `new_${Date.now()}`,
      name: "",
      stateExceptions: [],
      action: "CONTINUE",
      delayAfterFailure: 0,
      nextState: null,
    };
    setRetrySchedule({
      ...retrySchedule,
      states: [...retrySchedule.states, newDataItem],
    });
  };

  const addNewStateException = (): void => {
    const defaultState =
      retrySchedule.states.length > 0 ? retrySchedule.states[0] : null;
    const newDataItem: StateException = {
      id: `new_${Date.now()}`,
      inEdit: true,
      stateToApplyException: defaultState ? defaultState.name : null,
    };
    setAllStateExceptions([...allStateExceptions, newDataItem]);
  };

  function onStateChange(event: GridItemChangeEvent): void {
    const foundPosition = retrySchedule.states.findIndex(
      (s) =>
        (s.id && s.id === event.dataItem.id) ||
        (!s.id && s.name === event.dataItem.name)
    );

    if (foundPosition === -1) {
      console.error("Could not find matching state to update", event.dataItem);
      return;
    }

    const items = [...retrySchedule.states];
    const itemValue =
      event.value?.id || event.value?.value || event.value?.name || event.value;

    const updatedItem = {
      ...items[foundPosition],
      [event.field]: itemValue,
    };

    // Check if the action field is being changed to STOP
    if (
      event.field === "action" &&
      (itemValue === "STOP" || itemValue?.value === "STOP")
    ) {
      updatedItem.nextState = null;
      updatedItem.delayAfterFailure = null;

      // Clear validation errors for this state if they exist
      if (updatedItem.id && errors.states && errors.states[updatedItem.id]) {
        const newErrors = { ...errors };
        if (newErrors.states[updatedItem.id]) {
          delete newErrors.states[updatedItem.id].nextState;
          delete newErrors.states[updatedItem.id].delayAfterFailure;

          // If no more errors for this state, remove the state entry
          if (Object.keys(newErrors.states[updatedItem.id]).length === 0) {
            delete newErrors.states[updatedItem.id];
          }

          setErrors(newErrors);
        }
      }
    } else if (
      updatedItem.action === "STOP" ||
      (typeof updatedItem.action === "object" &&
        updatedItem.action?.value === "STOP")
    ) {
      updatedItem.nextState = null;
      updatedItem.delayAfterFailure = null;
    }

    items.splice(foundPosition, 1, updatedItem);
    setRetrySchedule({
      ...retrySchedule,
      states: items,
    });
  }

  function onStateExceptionChange(event: GridItemChangeEvent): void {
    const foundPosition = allStateExceptions.findIndex(
      (s) => s.id && s.id === event.dataItem.id
    );

    if (foundPosition === -1) {
      console.error(
        "Could not find matching state exception to update",
        event.dataItem
      );
      return;
    }

    const items = allStateExceptions.map((item) => ({ ...item }));
    const itemValue =
      event.value?.name || event.value?.id || event.value?.value
        ? event.value
        : event.value?.value ||
          event.value?.name ||
          event.value?.id ||
          event.value;

    const updatedItem = {
      ...items[foundPosition],
      [event.field]: itemValue,
    };

    // Check if the action field is being changed to STOP
    if (
      event.field === "action" &&
      (itemValue === "STOP" || itemValue?.value === "STOP")
    ) {
      updatedItem.nextState = null;
      updatedItem.delayAfterFailure = null;

      // Clear validation errors for this exception if they exist
      if (
        updatedItem.id &&
        errors.exceptions &&
        errors.exceptions[updatedItem.id]
      ) {
        const newErrors = { ...errors };
        if (newErrors.exceptions[updatedItem.id]) {
          delete newErrors.exceptions[updatedItem.id].nextState;
          delete newErrors.exceptions[updatedItem.id].delayAfterFailure;

          // If no more errors for this exception, remove the exception entry
          if (Object.keys(newErrors.exceptions[updatedItem.id]).length === 0) {
            delete newErrors.exceptions[updatedItem.id];
          }

          setErrors(newErrors);
        }
      }
    } else if (
      updatedItem.action === "STOP" ||
      updatedItem.action?.value === "STOP"
    ) {
      updatedItem.nextState = null;
      updatedItem.delayAfterFailure = null;
    }

    items.splice(foundPosition, 1, updatedItem);
    setAllStateExceptions(items);
  }

  const validateState = (state: ScheduleState): StateErrors => {
    const errors: StateErrors = {};

    if (!state.name) {
      errors.name =
        i18n["smsc.deliveryAndRetryScheduleForm.error.nameRequired"] ||
        "Name is required";
    }

    if (!state.action) {
      errors.action =
        i18n["smsc.deliveryAndRetryScheduleForm.error.actionRequired"] ||
        "Action is required";
    }

    const actionValue =
      typeof state.action === "object" ? state.action.value : state.action;
    if (actionValue !== "STOP") {
      if (!state.nextState) {
        errors.nextState =
          i18n["smsc.deliveryAndRetryScheduleForm.error.nextStateRequired"] ||
          "Next state is required";
      }

      if (
        typeof state.delayAfterFailure === "undefined" ||
        state.delayAfterFailure === null ||
        state.delayAfterFailure < 0
      ) {
        errors.delayAfterFailure =
          i18n["smsc.deliveryAndRetryScheduleForm.error.delayPositive"] ||
          "Delay must be a positive number";
      }
    }

    return errors;
  };

  const validateStateException = (
    exception: StateException
  ): StateExceptionErrors => {
    const errors: StateExceptionErrors = {};

    if (!exception.mapErrorCode) {
      errors.mapErrorCode =
        i18n["smsc.deliveryAndRetryScheduleForm.error.mapErrorCodeRequired"] ||
        "MAP error code is required";
    }

    if (!exception.action) {
      errors.action =
        i18n["smsc.deliveryAndRetryScheduleForm.error.actionRequired"] ||
        "Action is required";
    }

    const actionValue =
      typeof exception.action === "object"
        ? exception.action.value
        : exception.action;
    if (actionValue !== "STOP") {
      if (!exception.nextState) {
        errors.nextState =
          i18n["smsc.deliveryAndRetryScheduleForm.error.nextStateRequired"] ||
          "Next state is required";
      }

      if (
        typeof exception.delayAfterFailure === "undefined" ||
        exception.delayAfterFailure === null ||
        exception.delayAfterFailure < 0
      ) {
        errors.delayAfterFailure =
          i18n["smsc.deliveryAndRetryScheduleForm.error.delayPositive"] ||
          "Delay must be a positive number";
      }
    }

    return errors;
  };

  return (
    <div style={{ paddingTop: "2em" }}>
      <ValidatorForm onSubmit={handleSubmit} className="tango-form">
        <div style={{ marginLeft: "1em" }}>
          <Card>
            <CardContent>
            <ErrorsDisplay
              errorResponse={errorResponse}
              keyPrefix="smsws.retrySchedule.form.keys"
            />
            <TextValidator
              label={
                i18n["smsc.deliveryAndRetryScheduleForm.nameLabel"] || "Name"
              }
              onChange={handleChange}
              name="name"
              value={retrySchedule.name}
              validators={["required", "maxStringLength:50"]}
              errorMessages={[
                i18n["smsc.deliveryAndRetryScheduleForm.error.nameRequired"] ||
                  "Name is required",
                i18n["smsc.deliveryAndRetryScheduleForm.error.nameMaxLength"] ||
                  "Name cannot exceed 50 characters",
              ]}
            />

            <Accordion defaultExpanded>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <span className="accordion-title">
                  {i18n[
                    "smsc.deliveryAndRetryScheduleForm.accordion.scheduleStates"
                  ] || "Schedule States"}
                </span>
              </AccordionSummary>
              <AccordionDetails>
                <ScheduleStatesGrid
                  retrySchedule={retrySchedule}
                  enumerations={enumerations}
                  onStateChange={onStateChange}
                  onAddNew={addNewState}
                  setRetrySchedule={setRetrySchedule}
                  validateState={validateState}
                  getErrorMessage={getErrorMessage}
                  i18n={i18n}
                />
              </AccordionDetails>
            </Accordion>

            <Accordion defaultExpanded>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <span className="accordion-title">
                  {i18n[
                    "smsc.deliveryAndRetryScheduleForm.accordion.scheduleExceptions"
                  ] || "Schedule Exceptions"}
                </span>
              </AccordionSummary>
              <AccordionDetails>
                <ScheduleExceptionsGrid
                  allStateExceptions={allStateExceptions}
                  retrySchedule={retrySchedule}
                  enumerations={enumerations}
                  onStateExceptionChange={onStateExceptionChange}
                  onAddNew={addNewStateException}
                  setAllStateExceptions={setAllStateExceptions}
                  setRetrySchedule={setRetrySchedule}
                  validateStateException={validateStateException}
                  i18n={i18n}
                />
              </AccordionDetails>
            </Accordion>
          </CardContent>
          <CardActions>
            <CardActions className="card-actions content-card-actions">
              <Button
                variant="contained"
                color="secondary"
                type="button"
                onClick={goBack.bind(this)}
              >
                {i18n["button.cancel"] || "Cancel"}
              </Button>
              {SecurityService.checkPermission(
                "SMSC_DELIVERY_RETRY_SCHEDULES_UPDATE_PERMISSION"
              ) && (
                <Button
                  variant="contained"
                  color="primary"
                  type="submit"
                  className="request-handler-submit-button"
                >
                  {i18n["button.submit"] || "Submit"}
                </Button>
              )}
            </CardActions>
          </CardActions>
        </Card>
      </div>
    </ValidatorForm>
    </div>
  );
}
