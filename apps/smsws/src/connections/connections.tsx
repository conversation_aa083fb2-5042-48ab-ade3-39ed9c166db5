import { <PERSON><PERSON>, Card, CardContent, MenuItem, TextField } from "@mui/material";
import { MaterialTable, tableIcons } from "@pnmui/common";
import { $i18n } from "@pnmui/common/services/propertiesService";
import { SecurityService } from "@pnmui/common/services/securityService";
import React, { ChangeEvent, useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { toast } from "react-toastify";
import { tap } from "rxjs/operators";

import { SmscApplicationsService } from "@pnmui/common/services/smsc/applicationsService";
import { SmscConnectionsService } from "@pnmui/common/services/smsc/connectionsService";
import { paginate } from "@pnmui/common/utils";
import {
  SelectValidator,
  ValidatorForm,
} from "react-material-ui-form-validator";
import { Application } from "../types";

interface Connection extends Application {
  systemId?: string;
  applicationSelectionStrategy?: string;
  application?: Application;
}

interface TableData {
  data: Connection[];
  page: number;
  totalCount: number;
}

function SmscConnections() {
  const [originalConnections, setOriginalConnections] = useState<Connection[]>([]);
  const [connections, setConnections] = useState<Connection[]>([]);
  const [i18n, setI18n] = useState<Record<string, string>>({});
  const [selectedRows, setSelectedRows] = useState<(string | number)[]>([]);
  const [selectedName, setSelectedName] = useState<string>("");
  const [hostName, setHostName] = useState<string>("");
  const navigate = useNavigate();
  const tableRefConnections = React.createRef<any>();

  useEffect(() => {
    $i18n.pipe(tap()).subscribe((i18NProps) => {
      if (i18NProps) {
        setI18n(i18NProps);
      }
    });
    populateConnections();
  }, []);

  async function populateConnections(): Promise<void> {
    const applications = await SmscApplicationsService.getApplications();
    try {
      const connectionsResponse = await SmscConnectionsService.getConnections();
      const connectionsList: Connection[] = Array.isArray(connectionsResponse)
        ? connectionsResponse.map((c: any) => ({
            ...c,
            application: applications.find(
              (a) => a.id === (c.application?.id || c.application)
            ),
          }))
        : [];
      setConnections(connectionsList);
      setOriginalConnections(connectionsList);
    } catch (error) {
      console.error("Error fetching connections:", error);
    }
  }

  async function populateConnectionsData(query: { page: number; pageSize: number }): Promise<TableData> {
    const paginatedList = paginate(connections, query.page, query.pageSize);
    return {
      data: paginatedList,
      page: query.page,
      totalCount: connections.length,
    };
  }

  const handleRowSelection = (rows: Connection[]): void => {
    setSelectedRows(rows.map((row) => row.id || ''));
  };

  async function handleReload(event: React.FormEvent): Promise<void> {
    event.preventDefault();
    try {
      await SmscConnectionsService.loadFromFileSystem('connections', hostName);
      toast.success(
        i18n["smsc.connections.reloadSuccess"] ||
          "Connections reloaded successfully"
      );
      await populateConnections();
    } catch (error: any) {
      console.error("Error reloading connections:", error);
      toast.error(
        i18n["smsc.connections.reloadFailure"] ||
          "Failed to reload connections"
      );
    }
  }

  const handleDelete = async (): Promise<void> => {
    if (selectedRows.length === 0) {
      console.error("No rows selected for deletion.");
      return;
    }
    if (window.confirm("Are you sure you want to delete?")) {
      try {
        await Promise.all(
          selectedRows.map(async (id) => {
            return await SmscConnectionsService.deleteConnectionById(id);
          })
        );
        console.log("selectedRows", selectedRows);
        console.log(
          "connections",
          connections.map((r) => r.id)
        );
        const updatedConnections = connections.filter(
          (pool) => pool.id !== undefined && selectedRows.indexOf(pool.id) === -1
        );
        console.log(
          "updatedConnections",
          updatedConnections.map((r) => r.id)
        );
        setConnections([...updatedConnections]);
        setSelectedRows([]);
        toast.success("Rows deleted successfully!");
      } catch (error: any) {
        console.error(error);
        toast.error(error.message);
      }
    }
  };

  useEffect(() => {
    tableRefConnections.current && tableRefConnections.current.onQueryChange();
  }, [connections]);

  useEffect(() => {
    search();
  }, [selectedName]);

  function search(): void {
    const filteredConnections = originalConnections.filter((pool) =>
      pool.name?.toLowerCase().includes(selectedName.toLowerCase())
    );
    setConnections(filteredConnections);
  }

  return (
    <div className="wrapper">
      <Card className="content-card">
        <div className="form-row" style={{ display: "flex", alignItems: "flex-end", marginBottom: "1rem" }}>

          <div style={{ display: "flex", alignItems: "center" }}>
            <TextField
              label={i18n["smsc.resourcePolicies.hostName"] || "Host name"}
              variant="outlined"
              value={hostName}
              onChange={(e: ChangeEvent<HTMLInputElement>) =>
                setHostName(e.target.value)
              }
              style={{ marginRight: "1rem", minWidth: "15em" }}
              margin="normal"
            />

            <Button
              variant="contained"
              color="primary"
              onClick={handleReload}
              className="request-handler-reload-button"
              aria-label="Load from File"
              disabled={
                !SecurityService.checkPermission(
                  "SMSC_CONNECTIONS_READ_PERMISSION"
                ) || connections.length > 0
              }
              style={{
                display: connections.length > 0 ? 'none' : 'inline-flex',
                marginBottom: "8px"
              }}
            >
              {i18n["button.reload"] || "Load from File"}
            </Button>
          </div>

          <ValidatorForm onSubmit={() => {}} style={{ marginBottom: 0 }}>
            <SelectValidator
              name="connectionName"
              label={
                i18n["smsc.connections.connectionList.name"] || "Name"
              }
              margin="normal"
              variant="outlined"
              style={{
                minWidth: "15em",
                marginRight: "1rem",
              }}
              children={["Any", ...connections.map((c) => c.name)].map(
                (name) => (
                  <MenuItem key={name} value={name === "Any" ? "" : name}>
                    {name}
                  </MenuItem>
                )
              )}
              onChange={(e: any) => setSelectedName(e.target.value)}
              value={selectedName}
            />
          </ValidatorForm>
        </div>

        <CardContent>
          <MaterialTable
            tableRef={tableRefConnections}
            icons={tableIcons}
            data={populateConnectionsData}
            columns={[
              {
                field: "name",
                title: i18n["smsc.connections.connectionList.name"] || "Name",
              },
              {
                field: "systemId",
                title:
                  i18n["smsc.connections.connectionList.systemId"] ||
                  "System ID",
              },
              {
                field: "application.name",
                title:
                  i18n["smsc.connections.connectionList.application"] ||
                  "Application",
                render: (row: Connection) => {
                  return row.applicationSelectionStrategy ===
                    "BASED_ON_NETWORK_ADDRESS"
                    ? "Based on Network Address"
                    : row.application?.name || "";
                },
              },
            ]}
            options={{
              selection: true,
              actionsColumnIndex: -1,
              toolbar: false,
              pageSize: 20,
              pageSizeOptions: [10, 20, 50],
              emptyRowsWhenPaging: false,
              headerStyle: { fontWeight: "bold" },
            }}
            onRowClick={(_event, rowData: Connection) => {
              navigate(`/smscConnectionForm/${rowData.id}`);
            }}
            onSelectionChange={(rows: Connection[]) => handleRowSelection(rows)}
          />
        </CardContent>
        <CardContent>
          <Button
            variant="contained"
            color="primary"
            className="request-handler-add-button"
            aria-label="Add"
            onClick={() => navigate("/smscConnectionForm/new")}
            style={{
              marginLeft: "15px",
              marginTop: "2rem",
              marginBottom: "1rem",
            }}
            disabled={
              !SecurityService.checkPermission(
                "SMSC_CONNECTION_CREATE_PERMISSION"
              )
            }
          >
            {i18n["button.add"] || "Add"}
          </Button>

          <span style={{ marginLeft: "10px" }}>
            <Button
              variant="contained"
              color="secondary"
              type="button"
              onClick={handleDelete}
              style={{ marginTop: "2rem", marginBottom: "1rem" }}
              disabled={
                !SecurityService.checkPermission(
                  "SMSC_CONNECTION_DELETE_PERMISSION"
                )
              }
            >
              {i18n["button.delete"] || "Delete"}
            </Button>
          </span>
        </CardContent>
      </Card>
    </div>
  );
}

export default SmscConnections;





