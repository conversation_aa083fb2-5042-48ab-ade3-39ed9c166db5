// Import process polyfill first to ensure it's available for all modules
import './processPolyfill'

// Import KendoReact license - this must be imported before any KendoReact components
import './kendoLicense.ts'

// Import KendoReact theme
import '@progress/kendo-theme-material/dist/all.css'

import React from 'react'
import ReactDOM from 'react-dom/client'
import App from './App'
import './index.css'

// Import and initialize services from common package
import { PropertiesService, $properties } from '@pnmui/common/services/propertiesService'
import { SecurityService } from '@pnmui/common/services/securityService'

console.log('main.tsx: Starting application initialization')
console.log('main.tsx: Environment variables:', process.env)

// Initialize services and render app only after services are initialized
console.log('main.tsx: Starting service initialization')

// Create a function to render the app
const renderApp = () => {
  console.log('main.tsx: Rendering app')
  ReactDOM.createRoot(document.getElementById('root')!).render(
    <React.StrictMode>
      <App />
    </React.StrictMode>,
  )
}



// Set default properties in case initialization fails
$properties.next({
  "client.smsws.base.url": "http://localhost:3500",
  "client.pcrf.base.url": "http://localhost:3500",
  "client.spcm.base.url": "http://localhost:3500",
  "client.rte.base.url": "http://localhost:3500/campaign-manager",
  "client.ims.base.url": "http://localhost:3500",
  "client.docstore.base.url": "http://localhost:3500",
  "client.sqs.enabled": "true"
});

// Initialize services first, then render the app
Promise.all([
  PropertiesService.init().catch(error => {
    console.error('main.tsx: Error initializing PropertiesService:', error);
    return Promise.resolve(); // Continue despite error
  }),
  SecurityService.init().catch(error => {
    console.error('main.tsx: Error initializing SecurityService:', error);
    return Promise.resolve(); // Continue despite error
  })
])
  .then(() => {
    console.log('main.tsx: Services initialized successfully')
    console.log('main.tsx: Properties after initialization:', $properties.value)
    console.log('main.tsx: client.smsws.base.url:', $properties.value["client.smsws.base.url"])

    // Only render the app after services are initialized
    renderApp()
  })
  .catch(error => {
    console.error('main.tsx: Error initializing services:', error)
    // Render the app anyway, but log the error
    console.warn('main.tsx: Rendering app despite service initialization error')
    renderApp()
  })
