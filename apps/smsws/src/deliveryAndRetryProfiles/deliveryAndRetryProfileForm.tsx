import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Autocomplete,
  Button,
  Card,
  CardActions,
  CardContent,
  CardHeader,
  FormControlLabel,
  Paper,
  Switch,
} from "@mui/material";
import { ComboBoxCell, CommandCell, SelectCell } from "@pnmui/common";
import { $i18n } from "@pnmui/common/services/propertiesService";
import { SecurityService } from "@pnmui/common/services/securityService";
import { SmscDeliveryAndRetryProfilesService } from "@pnmui/common/services/smsc/deliveryAndRetryProfilesService";
import { SmscDeliveryAndRetrySchedulesService } from "@pnmui/common/services/smsc/deliveryAndRetrySchedulesService";
import { SmscEnumerationsService } from "@pnmui/common/services/smsc/enumerationsService";
import { GridItemChangeEvent, GridToolbar } from "@progress/kendo-react-grid";
import { ChangeEvent, useEffect, useState } from "react";
import { TextValidator, ValidatorForm } from "react-material-ui-form-validator";
import { useNavigate, useParams } from "react-router-dom";
import { toast } from "react-toastify";
import { tap } from "rxjs/operators";
import "../common.css";
import { Column, Grid } from "../components/KendoGridWrapper";
import { DeliveryAndRetrySchedule } from "../types";

interface EnumerationOption {
  value: string;
  displayText: string;
}

interface Enumerations {
  mapErrorCode?: EnumerationOption[];
  [key: string]: EnumerationOption[] | undefined;
}

interface ErrorSchedule {
  id: string | number;
  mapErrorCode: EnumerationOption | string;
  retrySchedule: DeliveryAndRetrySchedule | null;
  delayAfterFailure?: number;
  inEdit?: boolean;
}

interface RetryProfile {
  id?: string | number;
  name: string;
  defaultDeliveryAndRetrySchedule: DeliveryAndRetrySchedule | null;
  delayBeforeFirstDelivery: number;
  maxExpiryTime: number;
  maxDeliveryAttempts: number;
  sriRetryPriorityEnabled: boolean;
  validityPeriodEnabled: boolean;
  deliveryErrorScheduleMap: ErrorSchedule[];
}

interface ErrorScheduleErrors {
  mapErrorCode?: string;
  retrySchedule?: string;
  delayAfterFailure?: string;
}

interface FormErrors {
  deliveryErrorScheduleMap?: {
    [key: string]: ErrorScheduleErrors;
  };
}

const SmscDeliveryAndRetryProfileForm = () => {
  const navigate = useNavigate();

  const [i18n, setI18n] = useState<Record<string, string>>({});
  const params = useParams<{ id: string }>();
  const [enumerations, setEnumerations] = useState<Enumerations>({});
  const [retrySchedules, setRetrySchedules] = useState<DeliveryAndRetrySchedule[]>([]);
  const [retryScheduleInputValue, setRetryScheduleInputValue] = useState<string>("");
  const [retryProfile, setRetryProfile] = useState<RetryProfile>({
    name: "",
    defaultDeliveryAndRetrySchedule: null,
    delayBeforeFirstDelivery: 0,
    maxExpiryTime: 0,
    maxDeliveryAttempts: 0,
    sriRetryPriorityEnabled: false,
    validityPeriodEnabled: false,
    deliveryErrorScheduleMap: [],
  });
  const [errors, setErrors] = useState<FormErrors>({});

  useEffect(() => {
    async function fetchData() {
      const [enumerationsData, retrySchedulesData, profileData] =
        await Promise.all([
          SmscEnumerationsService.getEnumerations(),
          SmscDeliveryAndRetrySchedulesService.getDeliveryAndRetrySchedules(),
          params["id"] && params["id"] !== "new"
            ? SmscDeliveryAndRetryProfilesService.getRetryProfileById(
                params["id"]
              )
            : Promise.resolve(null),
        ]);

      setEnumerations(enumerationsData);
      setRetrySchedules(retrySchedulesData);

      if (profileData) {
        const mappedDeliveryErrorScheduleMap =
          profileData.deliveryErrorScheduleMap.map((item) => {
            let mappedRetrySchedule = null;
            if (item.deliveryAndRetrySchedule) {
              mappedRetrySchedule =
                retrySchedulesData.find(
                  (s) => s.id === item.deliveryAndRetrySchedule
                ) || null;
            }

            return {
              ...item,
              mapErrorCode:
                enumerationsData.mapErrorCode?.find(
                  (m) => m.value === item.mapErrorCode
                ) || item.mapErrorCode,
              retrySchedule: mappedRetrySchedule,
            };
          });

        let mappedDefaultSchedule = null;
        if (profileData.defaultDeliveryAndRetrySchedule) {
          mappedDefaultSchedule =
            retrySchedulesData.find(
              (s) => s.id === profileData.defaultDeliveryAndRetrySchedule
            ) || null;
        }

        setRetryProfile({
          ...profileData,
          deliveryErrorScheduleMap: mappedDeliveryErrorScheduleMap,
          defaultDeliveryAndRetrySchedule: mappedDefaultSchedule,
        });
      }
    }

    fetchData();
  }, [params]);

  useEffect(() => {
    $i18n.pipe(tap()).subscribe((i18NProps) => {
      if (i18NProps) {
        setI18n(i18NProps);
      }
    });
    console.log("retryProfile", retryProfile);
  }, [retryProfile]);

  const handleChange = (e: ChangeEvent<HTMLInputElement | { name?: string; value: unknown }>) => {
    if (!e.target.name) return;

    if (e.target.name.indexOf(".") > -1) {
      const [parent, child] = e.target.name.split(".");
      setRetryProfile({
        ...retryProfile,
        [parent]: {
          ...retryProfile[parent as keyof RetryProfile],
          [child]: e.target.value,
        },
      });
    } else {
      setRetryProfile({
        ...retryProfile,
        [e.target.name]: e.target.value,
      });
    }
  };

  const handleSubmit = (): void => {
    // Validate all items in deliveryErrorScheduleMap
    let hasErrors = false;
    const newErrors: FormErrors = { deliveryErrorScheduleMap: {} };

    retryProfile.deliveryErrorScheduleMap.forEach((schedule) => {
      const validationErrors = validateErrorSchedule(schedule);
      if (Object.keys(validationErrors).length > 0) {
        hasErrors = true;
        if (newErrors.deliveryErrorScheduleMap) {
          newErrors.deliveryErrorScheduleMap[schedule.id] = validationErrors;
        }
        // Show first error for this schedule
        toast.error(Object.values(validationErrors)[0]);
      }
    });

    if (hasErrors) {
      setErrors(newErrors);
      return;
    }

    // If validation passes, proceed with save
    SmscDeliveryAndRetryProfilesService.saveRetryProfile({
      ...retryProfile,
      defaultDeliveryAndRetrySchedule:
        retryProfile.defaultDeliveryAndRetrySchedule?.id || null,
      deliveryErrorScheduleMap: retryProfile.deliveryErrorScheduleMap.map(
        (r) => ({
          mapErrorCode: r.mapErrorCode?.value || r.mapErrorCode,
          deliveryAndRetrySchedule: r.retrySchedule?.id || r.retrySchedule,
          delayAfterFailure: r.delayAfterFailure
        })
      ),
    })
      .then((data) => {
        toast.success(
          i18n["smsc.retryProfileForm.SaveSuccess"] ||
            "RetryProfile saved successfully"
        );
        navigate("/smscDeliveryAndRetryProfiles");
      })
      .catch((error: any) => {
        console.error("handleSubmit error", error);
        toast.error(
          i18n["smsc.retryProfileForm.SaveFailure"] ||
            "Failed to save retryProfile: " + error.message
        );
      });
  };

  function goBack(): void {
    navigate(-1);
  }

  const addNew = (): void => {
    const newDataItem: ErrorSchedule = {
      id: Date.now(),
      inEdit: true,
      mapErrorCode: "",
      retrySchedule: null,
    };
    setRetryProfile({
      ...retryProfile,
      deliveryErrorScheduleMap: [
        newDataItem,
        ...retryProfile.deliveryErrorScheduleMap,
      ],
    });
  };

  function onItemChange(event: GridItemChangeEvent): void {
    const { dataItem, field, value } = event;
    const foundPosition = retryProfile.deliveryErrorScheduleMap.findIndex(
      (s) => s.id === dataItem.id
    );

    if (foundPosition !== -1) {
      const advancedProps = [...retryProfile.deliveryErrorScheduleMap];
      advancedProps[foundPosition] = {
        ...advancedProps[foundPosition],
        [field]: value,
        inEdit: true
      };
      setRetryProfile({
        ...retryProfile,
        deliveryErrorScheduleMap: advancedProps,
      });

      if (errors?.deliveryErrorScheduleMap?.[dataItem.id]?.[field as keyof ErrorScheduleErrors]) {
        setErrors(prev => ({
          ...prev,
          deliveryErrorScheduleMap: {
            ...prev.deliveryErrorScheduleMap,
            [dataItem.id]: {
              ...prev.deliveryErrorScheduleMap?.[dataItem.id],
              [field as keyof ErrorScheduleErrors]: undefined
            }
          }
        }));
      }
    }
  }

  function addNewRetrySchedule(): void {
    const newRetrySchedule = {
      name: retryScheduleInputValue,
    };
    SmscDeliveryAndRetrySchedulesService.saveRetryScheduleWithName(
      newRetrySchedule
    ).then((data: DeliveryAndRetrySchedule) => {
      setRetrySchedules([...retrySchedules, data]);
      setRetryProfile({
        ...retryProfile,
        defaultDeliveryAndRetrySchedule: { id: data.id, name: data.name! },
      });
    });
  }

  const validateErrorSchedule = (schedule: ErrorSchedule): ErrorScheduleErrors => {
    const errors: ErrorScheduleErrors = {};

    if (!schedule.mapErrorCode) {
      errors.mapErrorCode = i18n["smsc.retryProfileForm.error.mapErrorCodeRequired"] || "MAP error code is required";
    }

    if (!schedule.retrySchedule) {
      errors.retrySchedule = i18n["smsc.retryProfileForm.error.retryScheduleRequired"] || "Retry schedule is required";
    }

    // if (!schedule.delayAfterFailure && schedule.delayAfterFailure !== 0) {
    //   errors.delayAfterFailure = i18n["smsc.retryProfileForm.error.delayRequired"] || "Delay is required";
    // } else if (schedule.delayAfterFailure < 0) {
    //   errors.delayAfterFailure = i18n["smsc.retryProfileForm.error.delayPositive"] || "Delay must be a positive number";
    // }

    return errors;
  };

  const getErrorMessage = (scheduleId: string | number, field: string): string | undefined => {
    return errors?.deliveryErrorScheduleMap?.[scheduleId]?.[field as keyof ErrorScheduleErrors];
  };

  return (
    <div style={{ padding: "0.5em", paddingTop: "2em" }}>
      <ValidatorForm onSubmit={handleSubmit} className="tango-form">
        <div style={{ marginLeft: "1em" }}>
          <Card>
            <CardContent>
              <TextValidator
                label={i18n["smsc.resourcePolicies.list.name"] || "Name"}
                onChange={handleChange}
                name="name"
                value={retryProfile.name}
                validators={["required"]}
                errorMessages={["Name is required"]}
              />

              <Accordion
                style={{ marginTop: "1em", borderTop: "none" }}
                defaultExpanded={true}
              >
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <span className={"accordion-title"}>
                    {
                      i18n[
                        "antispam.locationProfile.form.general.legend" ||
                          "General"
                      ]
                    }
                  </span>
                </AccordionSummary>
                <AccordionDetails>
                  <div
                    style={{
                      display: "flex",
                      flexDirection: "row",
                      alignItems: "center",
                      gap: "1em",
                    }}
                  >
                    <TextValidator
                      label={
                        i18n["smsc.retryProfileForm.FirstDeliverylabel"] ||
                        "Delay before first delivery attempt in seconds"
                      }
                      type="number"
                      suffix="ms"
                      onChange={handleChange}
                      name="delayBeforeFirstDelivery"
                      value={retryProfile?.delayBeforeFirstDelivery || 0}
                      validators={["required", "minNumber:0"]}
                      errorMessages={
                        i18n["smsc.retryProfileForm.FirstDeliveryRequired"] ||
                        "Delay before first delivery is required,Delay must be positive"
                      }
                      style={{ width: "12em" }}
                    />
                    <TextValidator
                      label={
                        i18n[
                          "smsc.retryProfileForm.MaxDeliveryAttemptsLabel"
                        ] || "Maximum delivery attempts"
                      }
                      type="number"
                      onChange={handleChange}
                      name="maxDeliveryAttempts"
                      value={retryProfile?.maxDeliveryAttempts || 0}
                      validators={["required", "minNumber:-1"]}
                      errorMessages={[
                        "Max delivery attempts is required",
                        "Max delivery attempts must be non-negative",
                      ]}
                      style={{ width: "12em" }}
                    />
                    <TextValidator
                      label={
                        i18n["smsc.retryProfileForm.MaxExpiryTimeLabel"] ||
                        "Maximum Expiry Time in minutes"
                      }
                      type="number"
                      onChange={handleChange}
                      name="maxExpiryTime"
                      value={retryProfile?.maxExpiryTime || 0}
                      validators={["required", "minNumber:-1"]}
                      errorMessages={[
                        "Max expiry time is required",
                        "Max expiry time must be positive",
                      ]}
                      style={{ width: "12em" }}
                    />
                    <FormControlLabel
                      style={{ width: "25em" }}
                      control={
                        <Switch
                          checked={
                            retryProfile && retryProfile.validityPeriodEnabled
                          }
                          onChange={(e) =>
                            setRetryProfile({
                              ...retryProfile,
                              validityPeriodEnabled: e.target.checked,
                            })
                          }
                        />
                      }
                      label={
                        i18n[
                          "smsc.retryProfileForm.ValidityPeriodEnabledLabel"
                        ] || "Validity Period Enabled"
                      }
                    />
                  </div>
                </AccordionDetails>
              </Accordion>
              <Accordion
                style={{ marginTop: "1em", borderTop: "none" }}
                defaultExpanded={true}
              >
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <span className={"accordion-title"}>
                    {i18n[
                      "smsc.retryProfileForm.DeliveryAndRetryScheduleAccordionTitle"
                    ] || "Delivery and Retry Schedule"}{" "}
                  </span>
                </AccordionSummary>
                <AccordionDetails className="flex-container">
                  <div style={{ marginTop: "0.1em" }}>
                    <p>
                      {
                        i18n[
                          "smsc.retryProfileForm.accordion.retrySchedules.details.error" ||
                            "Schedule to use if no specific MAP/Internal Error is matched"
                        ]
                      }
                    </p>
                    <Autocomplete
                      label={
                        i18n[
                          "smsc.retryProfileForm.DefaultRetryScheduleLabel"
                        ] || "Default Retry Schedule"
                      }
                      onChange={(e, value) => {
                        setRetryProfile({
                          ...retryProfile,
                          defaultDeliveryAndRetrySchedule: value,
                        });
                      }}
                      options={retrySchedules}
                      getOptionLabel={(option) => option.name || ""}
                      name="defaultDeliveryAndRetrySchedule"
                      value={
                        retryProfile.defaultDeliveryAndRetrySchedule || null
                      }
                      inputValue={retryScheduleInputValue}
                      style={{ width: "24.6em" }}
                      onInputChange={(event, newInputValue) => {
                        setRetryScheduleInputValue(newInputValue);
                      }}
                      renderInput={(params) => (
                        <TextValidator
                          {...params}
                          placeholder={
                            i18n[
                              "smsc.retryProfileForm.RetrySchedulePlaceholder"
                            ] ||
                            "Type for selecting or adding a Retry Schedule."
                          }
                          value={
                            retryProfile.defaultDeliveryAndRetrySchedule
                              ?.name || ""
                          }
                          validators={["required"]}
                          errorMessages={["Default Retry Schedule is required"]}
                        />
                      )}
                      PaperComponent={(e) => (
                        <Paper>
                          {e.children}
                          <Button
                            disabled={
                              !retryScheduleInputValue ||
                              !!retrySchedules.find(
                                (a) => a.name === retryScheduleInputValue
                              )
                            }
                            color="primary"
                            fullWidth
                            sx={{ justifyContent: "flex-start", pl: 2 }}
                            onMouseDown={(ev) => {
                              addNewRetrySchedule();
                            }}
                          >
                            {i18n["smsc.retryProfileForm.AddNewButton"] ||
                              "+ Add New"}
                          </Button>
                        </Paper>
                      )}
                    />
                  </div>
                </AccordionDetails>
              </Accordion>

              <Accordion
                style={{ marginTop: "1em", borderTop: "none" }}
                defaultExpanded={true}
              >
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <span className={"accordion-title"}>
                    {i18n[
                      "smsc.retryProfileForm.SpecificErrorSchedulesAccordionTitle"
                    ] || "Specific Error schedules"}{" "}
                  </span>
                </AccordionSummary>
                <AccordionDetails className="flex-container">
                  <div
                    style={{
                      width: "100%",
                      display: "flex",
                      flexDirection: "column",
                      alignItems: "flex-start",
                    }}
                  >
                    {/*  <p>
                      {
                        i18n[
                          "smsc.retryProfileForm.accordion.retrySchedules.specific.error" ||
                            "Here you configure which schedules will be used for specific MAP/internal errors on the initial delivery attempt"
                        ]
                      }
                    </p> */}
                    <p>
                      {i18n[
                        "smsc.retryProfileForm.SpecificErrorSchedulesDescription"
                      ] ||
                        "Here you configure which schedules will be used for specific MAP/internal errors on the initial delivery attempt"}
                    </p>
                    <Grid
                      editField="inEdit"
                      editable={true}
                      onItemChange={onItemChange}
                      style={{
                        width: "80em",
                      }}
                      data={
                        retryProfile.deliveryErrorScheduleMap
                          ? retryProfile.deliveryErrorScheduleMap.map((r) => ({
                              ...r,
                              mapErrorCode: r.mapErrorCode?.value
                                ? r.mapErrorCode
                                : enumerations.mapErrorCode?.find(
                                    (m) => m.value === r.mapErrorCode
                                  ),
                              retrySchedule: r.retrySchedule?.id
                                ? r.retrySchedule
                                : retrySchedules.find(
                                    (s) => s.id === r.retrySchedule
                                  ),
                            }))
                          : []
                      }
                      total={retryProfile.deliveryErrorScheduleMap?.length || 0}
                    >
                      <GridToolbar>
                        <button
                          type={"button"}
                          className="k-primary k-button k-grid-edit-command"
                          style={{ position: "absolute", right: "1em" }}
                          onClick={addNew}
                        >
                          {i18n["button.add" || "Add"]}
                        </button>
                      </GridToolbar>
                      <Column
                        field="mapErrorCode"
                        title={
                          i18n[
                            "smsc.retryProfileForm.MapInternalErrorColumn"
                          ] ||
                          "MAP/Internal Error"
                        }
                        editable={true}
                        cell={(props) => (
                          <SelectCell
                            {...props}
                            options={enumerations.mapErrorCode}
                            error={getErrorMessage(props.dataItem.id, 'mapErrorCode')}
                          />
                        )}
                      />
                      <Column
                        field="retrySchedule"
                        title={
                          i18n["smsc.retryProfileForm.RetryScheduleColumn"] ||
                          "Retry Schedule"
                        }
                        editable={true}
                        cell={(props) => {
                          const currentSchedule =
                            retrySchedules.find(
                              (s) => s.id === props.dataItem.retrySchedule?.id
                            ) || null;

                          return (
                            <ComboBoxCell
                              {...props}
                              options={retrySchedules}
                              value={currentSchedule}
                              error={getErrorMessage(props.dataItem.id, 'retrySchedule')}
                              onChange={(event) => {
                                let selectedSchedule = event.value;

                                if (
                                  selectedSchedule &&
                                  typeof selectedSchedule === "string"
                                ) {
                                  selectedSchedule =
                                    retrySchedules.find(
                                      (s) => s.name === selectedSchedule
                                    ) || null;
                                } else if (selectedSchedule?.id) {
                                  selectedSchedule =
                                    retrySchedules.find(
                                      (s) => s.id === selectedSchedule.id
                                    ) || selectedSchedule;
                                }

                                const updatedItems =
                                  retryProfile.deliveryErrorScheduleMap.map(
                                    (item) =>
                                      item.id === props.dataItem.id
                                        ? {
                                            ...item,
                                            retrySchedule: selectedSchedule,
                                          }
                                        : item
                                  );

                                setRetryProfile({
                                  ...retryProfile,
                                  deliveryErrorScheduleMap: updatedItems,
                                });
                              }}
                              onAddNew={(newValue) => {
                                SmscDeliveryAndRetrySchedulesService.saveRetryScheduleWithName(
                                  { name: newValue }
                                )
                                  .then((data) => {
                                    setRetrySchedules((prev) => [...prev, data]);

                                    const updatedItems =
                                      retryProfile.deliveryErrorScheduleMap.map(
                                        (item) =>
                                          item.id === props.dataItem.id
                                            ? { ...item, retrySchedule: data }
                                            : item
                                      );

                                    setRetryProfile({
                                      ...retryProfile,
                                      deliveryErrorScheduleMap: updatedItems,
                                    });
                                  })
                                  .catch((error) => {
                                    console.error(
                                      "Failed to add new retry schedule:",
                                      error
                                    );
                                    toast.error(
                                      i18n[
                                        "smsc.retryProfileForm.AddRetryScheduleFailure"
                                      ] || "Failed to add new retry schedule."
                                    );
                                  });
                              }}
                            />
                          );
                        }}
                      />
                      <Column
                        cell={(props) => (
                          <CommandCell
                            {...props}
                            item={retryProfile}
                            gridProp={"deliveryErrorScheduleMap"}
                            onChange={(data) => {
                              setRetryProfile({
                                ...retryProfile,
                                deliveryErrorScheduleMap: data,
                              });
                            }}
                            onSave={(dataItem) => {
                              setErrors((prev) => {
                                const newErrors = { ...prev };
                                if (newErrors.deliveryErrorScheduleMap) {
                                  delete newErrors.deliveryErrorScheduleMap[dataItem.id];
                                }
                                return newErrors;
                              });

                              const validationErrors = validateErrorSchedule(dataItem);

                              if (Object.keys(validationErrors).length > 0) {
                                setErrors((prev) => ({
                                  ...prev,
                                  deliveryErrorScheduleMap: {
                                    ...prev?.deliveryErrorScheduleMap,
                                    [dataItem.id]: validationErrors
                                  }
                                }));
                                return false;
                              }

                              const updatedItems = retryProfile.deliveryErrorScheduleMap.map(
                                (item) => item.id === dataItem.id ? { ...item, inEdit: false } : item
                              );

                              setRetryProfile({
                                ...retryProfile,
                                deliveryErrorScheduleMap: updatedItems,
                              });

                              return true;
                            }}
                          />
                        )}
                        filterable={false}
                      />
                    </Grid>
                  </div>
                </AccordionDetails>
              </Accordion>
            </CardContent>
            <CardActions className="card-actions content-card-actions">
              <Button
                variant="contained"
                color="secondary"
                type="button"
                onClick={goBack.bind(this)}
              >
                {i18n["button.cancel"] || "Cancel"}
              </Button>

              {SecurityService.checkPermission(
                "SMSC_DELIVERY_RETRY_PROFILES_UPDATE_PERMISSION"
              ) && (
                <Button
                  variant="contained"
                  color="primary"
                  type="submit"
                  className="request-handler-submit-button"
                >
                  {i18n["button.submit"] || "Submit"}
                </Button>
              )}
            </CardActions>
          </Card>
        </div>
      </ValidatorForm>
    </div>
  );
};

export default SmscDeliveryAndRetryProfileForm;





