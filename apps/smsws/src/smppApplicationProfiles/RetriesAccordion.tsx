import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Autocomplete,
  Button,
  FormControlLabel,
  MenuItem,
  Paper,
  Switch,
  TextField,
  Typography,
  AutocompleteChangeReason,
  AutocompleteInputChangeReason
} from "@mui/material";
import React, { useState, MouseEvent, ChangeEvent, SyntheticEvent } from "react";
import { SelectValidator } from "react-material-ui-form-validator";
import { SmscDeliveryAndRetryProfilesService } from "@pnmui/common/services/smsc/deliveryAndRetryProfilesService";
import { ApplicationProfile, DeliveryAndRetryProfile, EnumerationOption } from "../types";

interface RetriesSettings {
  enabled?: boolean;
  temporaryErrorRetryProfile?: string | number | DeliveryAndRetryProfile;
  permanentErrorRetryProfile?: string | number | DeliveryAndRetryProfile;
}

interface RetriesAccordionProps {
  applicationProfile: ApplicationProfile;
  setApplicationProfile: React.Dispatch<React.SetStateAction<ApplicationProfile>>;
  enumerations: Record<string, EnumerationOption[]>;
  deliveryAndRetryProfiles: DeliveryAndRetryProfile[];
  setDeliveryAndRetryProfiles: React.Dispatch<React.SetStateAction<DeliveryAndRetryProfile[]>>;
  errors: Record<string, any>;
}

const RetriesAccordion: React.FC<RetriesAccordionProps> = ({
  applicationProfile,
  setApplicationProfile,
  enumerations,
  deliveryAndRetryProfiles,
  setDeliveryAndRetryProfiles,
  errors,
}) => {
  const [inputValues, setInputValues] = useState<Record<string, string>>({});
  const handleAddNewRetryProfile = async (fieldName: string): Promise<void> => {
    const currentValue = inputValues[fieldName] || "";
    if (!currentValue) return;
    try {
      const newProfile =
        await SmscDeliveryAndRetryProfilesService.saveRetryProfileWithName(
          currentValue
        );
      setDeliveryAndRetryProfiles((prev) => [...prev, newProfile]);
      setApplicationProfile((prev) => ({
        ...prev,
        retries: { ...(prev.retries || {}), [fieldName]: newProfile.id },
      }));
      setInputValues((prev) => ({ ...prev, [fieldName]: "" }));
    } catch (error: any) {
      console.error("Error creating new retry profile:", error);
    }
  };

  const renderRetryProfileAutocomplete = (fieldName: string, label: string): JSX.Element => {
    const selectedProfile: DeliveryAndRetryProfile | null =
      deliveryAndRetryProfiles.find(
        (profile) => profile.id === applicationProfile.retries?.[fieldName]
      ) || null;

    return (
      <Autocomplete
        options={deliveryAndRetryProfiles}
        getOptionLabel={(option: DeliveryAndRetryProfile) => option.name || ""}
        value={selectedProfile}
        inputValue={inputValues[fieldName] || ""}
        onInputChange={(_: SyntheticEvent, newInputValue: string) => {
          setInputValues((prev) => ({ ...prev, [fieldName]: newInputValue }));
        }}
        onChange={(_: SyntheticEvent, value: DeliveryAndRetryProfile | null) => {
          setApplicationProfile((prev) => ({
            ...prev,
            retries: {
              ...prev.retries,
              [fieldName]: value ? value.id : null,
            },
          }));
        }}
        renderInput={(params) => <TextField {...params} label={label} error={!!errors?.retries?.[fieldName]} helperText={errors?.retries?.[fieldName]} />}
        PaperComponent={(props) => (
          <Paper {...props}>
            {props.children}
            <Button
              disabled={!inputValues[fieldName]}
              color="primary"
              fullWidth
              onMouseDown={(e: MouseEvent) => { e.preventDefault(); handleAddNewRetryProfile(fieldName); }}
              sx={{ justifyContent: "flex-start", pl: 2 }}
            >
              + Add New
            </Button>
          </Paper>
        )}
      />
    );
  };

  return (
    <Accordion style={{ marginTop: "1em", borderTop: "none" }} defaultExpanded>
      <AccordionSummary expandIcon={<ExpandMoreIcon />}>
        <Typography className="accordion-title">Retries</Typography>
      </AccordionSummary>
      <AccordionDetails
        className="flex-container"
        style={{ justifyContent: "start" }}
      >
        <FormControlLabel
          control={
            <Switch
              checked={applicationProfile.retries?.overrideMessagePriority}
              onChange={(e) =>
                setApplicationProfile((prev) => ({
                  ...prev,
                  retries: {
                    ...prev.retries,
                    overrideMessagePriority: e.target.checked,
                    alwaysUsePriority: e.target.checked
                      ? ""
                      : prev.retries?.priorityIfNoneSpecified || "",
                  },
                }))
              }
            />
          }
          label="Override Message Priority"
        />

        {applicationProfile.retries?.overrideMessagePriority ? (
          <>
            <SelectValidator
              label="Always Use Priority"
              onChange={(e) => {
                console.log('Always Use Priority changed:', e.target.value);
                setApplicationProfile((prev) => ({
                  ...prev,
                  retries: {
                    ...prev.retries,
                    alwaysUsePriority: e.target.value,
                  },
                }));
              }}
              name="retries.alwaysUsePriority"
              value={applicationProfile.retries?.alwaysUsePriority || ""}
              error={!!errors?.retries?.overridePriority}
              helperText={errors?.retries?.overridePriority}
            >
              {(enumerations?.priorityLevel || []).map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.displayText}
                </MenuItem>
              ))}
            </SelectValidator>

            {applicationProfile.retries.alwaysUsePriority === "LOW" &&
              renderRetryProfileAutocomplete(
                "lowPriorityDeliveryAndRetryProfile",
                "Low Priority Retry Profile"
              )}
            {applicationProfile.retries.alwaysUsePriority === "REGULAR" &&
              renderRetryProfileAutocomplete(
                "regularPriorityDeliveryAndRetryProfile",
                "Regular Priority Retry Profile"
              )}
            {applicationProfile.retries.alwaysUsePriority === "URGENT" &&
              renderRetryProfileAutocomplete(
                "urgentPriorityDeliveryAndRetryProfile",
                "Urgent Priority Retry Profile"
              )}
            {applicationProfile.retries.alwaysUsePriority === "EMERGENCY" &&
              renderRetryProfileAutocomplete(
                "emergencyPriorityDeliveryAndRetryProfile",
                "Emergency Priority Retry Profile"
              )}
          </>
        ) : (
          <>
            <SelectValidator
              label="Priority if None Specified"
              onChange={(e) => {
                setApplicationProfile((prev) => ({
                  ...prev,
                  retries: {
                    ...prev.retries,
                    priorityIfNoneSpecified: e.target.value,
                  },
                }));
              }}
              name="retries.priorityIfNoneSpecified"
              value={applicationProfile.retries?.priorityIfNoneSpecified || ""}
              error={!!errors?.retries?.defaultPriority}
              helperText={errors?.retries?.defaultPriority}
            >
              {(enumerations?.priorityLevel || []).map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.displayText}
                </MenuItem>
              ))}
            </SelectValidator>

            {renderRetryProfileAutocomplete(
              "lowPriorityDeliveryAndRetryProfile",
              "Low Priority Retry Profile"
            )}
            {renderRetryProfileAutocomplete(
              "regularPriorityDeliveryAndRetryProfile",
              "Regular Priority Retry Profile"
            )}
            {renderRetryProfileAutocomplete(
              "urgentPriorityDeliveryAndRetryProfile",
              "Urgent Priority Retry Profile"
            )}
            {renderRetryProfileAutocomplete(
              "emergencyPriorityDeliveryAndRetryProfile",
              "Emergency Priority Retry Profile"
            )}
          </>
        )}
      </AccordionDetails>
    </Accordion>
  );
};

export default RetriesAccordion;



