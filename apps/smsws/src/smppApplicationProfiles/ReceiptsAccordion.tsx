import ToggleOffIcon from "@mui/icons-material/ToggleOff";
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Autocomplete,
  Button,
  FormControlLabel,
  MenuItem,
  Paper,
  Switch,
  Typography,
  AutocompleteChangeReason,
  AutocompleteInputChangeReason
} from "@mui/material";
import React, { useEffect, useState, MouseEvent, ChangeEvent, SyntheticEvent } from "react";
import { SelectValidator, TextValidator } from "react-material-ui-form-validator";
import { SmscDeliveryAndRetryProfilesService } from "@pnmui/common/services/smsc/deliveryAndRetryProfilesService";
import { handleChange, handleSwitchChange } from "./common";
import { StorageFields } from "./StorageFields";
import { ApplicationProfile, DeliveryAndRetryProfile, ResourcePolicy } from "../types";

interface ReceiptSettings {
  enabled?: boolean;
  receiptDeliveryAndRetryProfile?: string | number | DeliveryAndRetryProfile;
  receiptResourcePolicy?: string | number | ResourcePolicy;
  receiptStoragePolicy?: string;
  receiptStorageDuration?: number;
  receiptStorageDurationUnit?: string;
}

interface ReceiptsAccordionProps {
  applicationProfile: ApplicationProfile;
  setApplicationProfile: React.Dispatch<React.SetStateAction<ApplicationProfile>>;
  resourcePolicies: ResourcePolicy[];
  deliveryAndRetryProfiles: DeliveryAndRetryProfile[];
  setDeliveryAndRetryProfiles: React.Dispatch<React.SetStateAction<DeliveryAndRetryProfile[]>>;
  errors: Record<string, any>;
  setErrors: React.Dispatch<React.SetStateAction<Record<string, any>>>;
}

const ReceiptsAccordion: React.FC<ReceiptsAccordionProps> = ({
  applicationProfile,
  setApplicationProfile,
  resourcePolicies,
  deliveryAndRetryProfiles = [],
  setDeliveryAndRetryProfiles,
  errors,
  setErrors
}) => {
  const [inputValue, setInputValue] = useState<string>("");
  const [expanded, setExpanded] = useState<boolean>(false);
  const [selectedProfile, setSelectedProfile] = useState<DeliveryAndRetryProfile | null>(
    applicationProfile.receipts?.receiptDeliveryAndRetryProfile?.id ||
    applicationProfile.receipts?.receiptDeliveryAndRetryProfile
      ? deliveryAndRetryProfiles.find(
          (r) =>
            r.id === applicationProfile.receipts?.receiptDeliveryAndRetryProfile ||
            r.id === applicationProfile.receipts?.receiptDeliveryAndRetryProfile?.id
        )
      : null
  );

  useEffect(() => {
    setExpanded(applicationProfile.receipts?.enabled || false);
  }, [applicationProfile.receipts?.enabled]);

  const validateReceipts = (): Record<string, string> => {
    const validationErrors: Record<string, string> = {};
    const receipts = applicationProfile.receipts;

    if (receipts?.enabled) {
      if (!receipts.receiptResourcePolicy) {
        validationErrors.receiptResourcePolicy = "Receipt resource policy is required";
      }
    }

    if (setErrors) {
      setErrors(prev => ({
        ...prev,
        receipts: Object.keys(validationErrors).length > 0 ? validationErrors : undefined
      }));
    }

    return validationErrors;
  };

  const handleToggle = (e: MouseEvent): void => {
    e.stopPropagation();
    const newEnabled = !applicationProfile.receipts?.enabled;
    setApplicationProfile((prev) => ({
      ...prev,
      receipts: {
        ...prev.receipts,
        enabled: newEnabled,
      },
    }));
    setExpanded(newEnabled);

    if (!newEnabled && setErrors) {
      setErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors.receipts;
        return newErrors;
      });
    } else {
      setTimeout(validateReceipts, 0);
    }
  };

  const addNewProfile = async (): Promise<void> => {
    if (!inputValue) return;
    try {
      const savedProfile = await SmscDeliveryAndRetryProfilesService.saveRetryProfileWithName(inputValue);
      setDeliveryAndRetryProfiles((prev) => [...prev, savedProfile]);
      setSelectedProfile(savedProfile);
      setApplicationProfile((prev) => ({
        ...prev,
        receipts: {
          ...prev.receipts,
          receiptDeliveryAndRetryProfile: savedProfile.id,
        },
      }));
      setInputValue("");
      if (applicationProfile.receipts?.enabled) {
        setTimeout(validateReceipts, 0);
      }
    } catch (error: any) {
      console.error("Failed to create new profile:", error);
    }
  };

  return (
    <Accordion
      style={{ marginTop: "1em", borderTop: "none" }}
      expanded={expanded}
    >
      <AccordionSummary
        expandIcon={
          <ToggleOffIcon
            style={{
              color: applicationProfile.receipts?.enabled ? "#3f51b5" : "gray",
              fontSize: "3em",
            }}
            onClick={handleToggle}
          />
        }
      >
        <Typography className="accordion-title">Enable Receipts</Typography>
      </AccordionSummary>
      <AccordionDetails className="flex-container" style={{ justifyContent: "start" }}>
        <div className="flex-container" style={{ justifyContent: "start" }}>
          <SelectValidator
            label="Receipt Resource Policy"
            onChange={(e) => {
              handleChange(e, setApplicationProfile);
              if (applicationProfile.receipts?.enabled) {
                setTimeout(validateReceipts, 0);
              }
            }}
            name="receipts.receiptResourcePolicy"
            value={applicationProfile.receipts?.receiptResourcePolicy || ""}
            validators={applicationProfile.receipts?.enabled ? ["required"] : []}
            errorMessages={["Receipt resource policy is required"]}
            error={Boolean(errors?.receipts?.receiptResourcePolicy)}
            helperText={errors?.receipts?.receiptResourcePolicy}
            style={{ minWidth: 200 }}
          >
            <MenuItem value="">
              <em>None</em>
            </MenuItem>
            {resourcePolicies.map((option) => (
              <MenuItem key={option.id} value={option.id}>
                {option.name}
              </MenuItem>
            ))}
          </SelectValidator>

          <Autocomplete
            value={selectedProfile}
            onChange={(_: SyntheticEvent, newValue: DeliveryAndRetryProfile | null) => {
              setSelectedProfile(newValue);
              setApplicationProfile((prev) => ({
                ...prev,
                receipts: {
                  ...prev.receipts,
                  receiptDeliveryAndRetryProfile: newValue ? newValue.id : null,
                },
              }));
              if (applicationProfile.receipts?.enabled) {
                setTimeout(validateReceipts, 0);
              }
            }}
            options={deliveryAndRetryProfiles}
            getOptionLabel={(option: DeliveryAndRetryProfile) => option?.name || ""}
            isOptionEqualToValue={(option: DeliveryAndRetryProfile, value: DeliveryAndRetryProfile) => option?.id === value?.id}
            style={{ width: "24.6em" }}
            renderInput={(params) => (
              <TextValidator
                {...params}
                label="Delivery and Receipt Retry Profile"
                placeholder="Type for selecting or adding a Profile"
                error={Boolean(errors?.receipts?.receiptDeliveryAndRetryProfile)}
                required={false}
                helperText={errors?.receipts?.receiptDeliveryAndRetryProfile}
                name="receipts.receiptDeliveryAndRetryProfile"
                value={selectedProfile?.id || ''}
              />
            )}
            inputValue={inputValue}
            onInputChange={(_: SyntheticEvent, newValue: string) => setInputValue(newValue)}
            PaperComponent={({ children }) => (
              <Paper>
                {children}
                <Button
                  color="primary"
                  fullWidth
                  disabled={!inputValue}
                  sx={{ justifyContent: "flex-start", pl: 2 }}
                  onMouseDown={(e: MouseEvent) => { e.preventDefault(); addNewProfile(); }}
                >
                  + Add New
                </Button>
              </Paper>
            )}
          />
          <FormControlLabel
            control={
              <Switch
                checked={applicationProfile.receiptStorage?.enabled || false}
                onChange={(e) => {
                  setApplicationProfile((prev) => ({
                    ...prev,
                    receiptStorage: {
                      ...prev.receiptStorage,
                      enabled: e.target.checked,
                    },
                  }));
                }}
              />
            }
            label="Enable Receipt Storage"
          />        </div>
        {applicationProfile.receiptStorage?.enabled && (
          <StorageFields
            onChange={(e) => handleChange(e, setApplicationProfile)}
            storage={applicationProfile.receiptStorage}
            propName={"receiptStorage"}
            map={resourcePolicies.map((option) => (
              <MenuItem key={option.id} value={option.id}>
                {option.name}
              </MenuItem>
            ))}
            onChangeSwitch={(e) => handleSwitchChange(e, setApplicationProfile)}
          />
        )}      </AccordionDetails>
    </Accordion>
  );
};

export default ReceiptsAccordion;



