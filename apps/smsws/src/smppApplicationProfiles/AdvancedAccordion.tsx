import ToggleOffIcon from "@mui/icons-material/ToggleOff";
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Typography,
} from "@mui/material";
import { GridToolbar, GridItemChangeEvent } from "@progress/kendo-react-grid";
import { Grid, Column } from "../components/KendoGridWrapper";
import React, { useCallback, useEffect, useState, MouseEvent } from "react";
import { toast } from "react-toastify";
import { SmscEnumerationsService } from "@pnmui/common/services/smsc/enumerationsService";
import { CommandCell, InputCell, SelectCell } from "@pnmui/common";
import { validateProperty } from "./validation";
import { ApplicationProfile, EnumerationOption } from "../types";

interface AdditionalProperty {
  id?: string | number;
  section?: string;
  name?: string;
  value?: string;
  inEdit?: boolean;
}

interface AdvancedSettings {
  enabled?: boolean;
  additionalProperties?: AdditionalProperty[];
}

interface AdvancedAccordionProps {
  applicationProfile: ApplicationProfile;
  setApplicationProfile: React.Dispatch<React.SetStateAction<ApplicationProfile>>;
  errors: Record<string, any>;
  setErrors: React.Dispatch<React.SetStateAction<Record<string, any>>>;
}

const AdvancedAccordion: React.FC<AdvancedAccordionProps> = ({ applicationProfile, setApplicationProfile, errors, setErrors }) => {
  const [expanded, setExpanded] = useState<boolean>(false);
  const [additionalPropertySections, setAdditionalPropertySections] = useState<EnumerationOption[]>(
    []
  );

  useEffect(() => {
    setExpanded(applicationProfile.advanced?.enabled || false);
  }, [applicationProfile.advanced?.enabled]);

  useEffect(() => {
    let isMounted = true;

    SmscEnumerationsService.getEnumerations().then((data) => {
      if (isMounted && data && data.additionalPropertySection) {
        const filteredSections = data.additionalPropertySection.filter((item) =>
          item.value.trim().startsWith("APPLICATION_PROFILE")
        );
        setAdditionalPropertySections(filteredSections);
      }
    });

    return () => {
      isMounted = false;
    };
  }, []);

  const handleToggle = (e: MouseEvent): void => {
    e.stopPropagation();
    const newEnabled = !applicationProfile.advanced?.enabled;
    setApplicationProfile((prev) => ({
      ...prev,
      advanced: { ...prev.advanced, enabled: newEnabled },
    }));
    setExpanded(newEnabled);

    if (!newEnabled && setErrors) {
      setErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors.advanced;
        return newErrors;
      });
    }
  };

  const addNewProperty = (): void => {
    const newProperty: AdditionalProperty = {
      id: `temp_${Date.now()}`,
      section: "",
      name: "",
      value: "",
      inEdit: true,
    };
    setApplicationProfile((prevProfile) => ({
      ...prevProfile,
      advanced: {
        ...prevProfile.advanced,
        properties: [newProperty, ...(prevProfile.advanced?.properties ?? [])],
      },
    }));
  };

  const onItemChange = useCallback((event: GridItemChangeEvent): void => {
    const { dataItem, field, value } = event;

    setApplicationProfile((prevProfile) => {
      const properties = prevProfile.advanced?.properties || [];
      const propertyIndex = properties.findIndex(p => p.id === dataItem.id);

      if (propertyIndex === -1) return prevProfile;

      const updatedProperties = [...properties];
      updatedProperties[propertyIndex] = {
        ...updatedProperties[propertyIndex],
        [field]: value,
        inEdit: true
      };

      return {
        ...prevProfile,
        advanced: {
          ...prevProfile.advanced,
          enabled: prevProfile.advanced?.enabled ?? false,
          properties: updatedProperties
        }
      };
    });
  }, []);

  const getErrorMessage = (propertyId: string | number | undefined, field: string): string | undefined => {
    if (!propertyId) return undefined;
    return errors?.advanced?.properties?.[propertyId]?.[field];
  };

  const handleSave = useCallback((dataItem: AdditionalProperty): boolean => {
    const completeDataItem = {
      ...dataItem,
      section: dataItem.section || '',
      name: dataItem.name || '',
      value: dataItem.value || ''
    };

    const validationErrors = validateProperty(completeDataItem);
    if (Object.keys(validationErrors).length > 0) {
      if (setErrors) {
        setErrors((prev) => ({
          ...prev,
          advanced: {
            ...prev?.advanced,
            properties: {
              ...prev?.advanced?.properties,
              [dataItem.id as string]: validationErrors
            }
          }
        }));
      }
      toast.error(Object.values(validationErrors)[0] as string);
      return false;
    }
    return true;
  }, [setErrors]);

  return (
    <Accordion
      style={{ marginTop: "1em", borderTop: "none" }}
      expanded={expanded}
    >
      <AccordionSummary
        expandIcon={
          <ToggleOffIcon
            style={{ color: expanded ? "#3f51b5" : "gray", fontSize: "3em" }}
            onClick={handleToggle}
          />
        }
      >
        <Typography className="accordion-title">Enable Advanced</Typography>
      </AccordionSummary>
      <AccordionDetails
        className="flex-container"
        style={{
          justifyContent: "start",
          flexDirection: "column",
          width: "100%",
        }}
      >
        <Typography variant="body1" style={{ marginTop: "1em" }}>
          Here you can configure additional properties. Refer to Admin Guide for
          full list of available properties
        </Typography>
        <Grid
          data={applicationProfile.advanced?.properties}
          total={applicationProfile.advanced?.properties?.length}
          style={{ width: "90%", marginTop: "1em" }}
        >
          <GridToolbar>
            <button
              type={"button"}
              className="k-primary k-button k-grid-edit-command"
              style={{ position: "absolute", right: "1em" }}
              onClick={addNewProperty}
            >
              Add
            </button>
          </GridToolbar>
          <Column
            field="section"
            title="Section"
            editable={true}
            cell={(props) => (
              <SelectCell
                {...props}
                onChange={onItemChange}
                options={additionalPropertySections}
                valueFunc={(o) => o.value}
                displayFunc={(o) => o.value}
                error={getErrorMessage(props.dataItem.id, 'section')}
              />
            )}
          />
          <Column
            field="propertyName"
            title="Property Name"
            cell={(props) => (
              <InputCell
                {...props}
                onChange={onItemChange}
                error={getErrorMessage(props.dataItem.id, 'propertyName')}
                debounceDelay={500}
              />
            )}
          />
          <Column
            field="propertyValue"
            title="Property Value"
            cell={(props) => (
              <InputCell
                {...props}
                onChange={onItemChange}
                error={getErrorMessage(props.dataItem.id, 'propertyValue')}
                debounceDelay={500}
              />
            )}
          />
          <Column
            cell={(props) => (
              <CommandCell
                {...props}
                item={applicationProfile.advanced}
                onChange={(data) => {
                  setApplicationProfile({
                    ...applicationProfile,
                    advanced: {
                      ...applicationProfile.advanced,
                      properties: data,
                    },
                  });
                }}
                onSave={handleSave}
                gridProp={"properties"}
              />
            )}
            filterable={false}
          />
        </Grid>
      </AccordionDetails>
    </Accordion>
  );
};

export default AdvancedAccordion;



