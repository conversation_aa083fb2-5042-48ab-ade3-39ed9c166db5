import ToggleOffIcon from "@mui/icons-material/ToggleOff";
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  FormControlLabel,
  Switch,
  Typography,
} from "@mui/material";
import React, { useEffect, useState, MouseEvent, ChangeEvent } from "react";
import { handleSwitchChange } from "./common";
import { ApplicationProfile } from "../types";

interface CDRSettings {
  enabled?: boolean;
  generateCDRsForDeliveryReceipts?: boolean;
  generateCDRsForSubmitSMResponses?: boolean;
  generateCDRsForIncomingMessages?: boolean;
}

interface CDRsAccordionProps {
  applicationProfile: ApplicationProfile;
  setApplicationProfile: React.Dispatch<React.SetStateAction<ApplicationProfile>>;
}

const CDRsAccordion: React.FC<CDRsAccordionProps> = ({ applicationProfile, setApplicationProfile }) => {
  const [expanded, setExpanded] = useState<boolean>(applicationProfile.cdrs?.enabled || false);

  useEffect(() => {
    setExpanded(applicationProfile.cdrs?.enabled || false);
  }, [applicationProfile.cdrs?.enabled]);

  const handleToggle = (e: MouseEvent): void => {
    e.stopPropagation();
    const newEnabled = !applicationProfile.cdrs?.enabled;
    setApplicationProfile((prev) => ({
      ...prev,
      cdrs: {
        ...prev.cdrs,
        enabled: newEnabled,
      },
    }));
    setExpanded(newEnabled);
  };

  return (
    <Accordion
      style={{ marginTop: "1em", borderTop: "none" }}
      expanded={expanded}
    >
      <AccordionSummary
        expandIcon={
          <ToggleOffIcon
            style={{
              color: applicationProfile.cdrs?.enabled ? "#3f51b5" : "gray",
              fontSize: "3em",
            }}
            onClick={handleToggle}
          />
        }
      >
        <Typography className="accordion-title">Enable CDRs</Typography>
      </AccordionSummary>
      <AccordionDetails
        className="flex-container"
        style={{ justifyContent: "start", flexDirection: "column" }}
      >
        <FormControlLabel
          control={
            <Switch
              checked={applicationProfile.cdrs?.submitCDR}
              onChange={(e: ChangeEvent<HTMLInputElement>) => handleSwitchChange(e, setApplicationProfile)}
              name="cdrs.submitCDR"
            />
          }
          label="Submit CDR"
        />
        <FormControlLabel
          control={
            <Switch
              checked={applicationProfile.cdrs?.retryCDR}
              onChange={(e: ChangeEvent<HTMLInputElement>) => handleSwitchChange(e, setApplicationProfile)}
              name="cdrs.retryCDR"
            />
          }
          label="Retry CDR"
        />
        <FormControlLabel
          control={
            <Switch
              checked={applicationProfile.cdrs?.chargingCDR}
              onChange={(e: ChangeEvent<HTMLInputElement>) => handleSwitchChange(e, setApplicationProfile)}
              name="cdrs.chargingCDR"
            />
          }
          label="Charging CDR"
        />
        <FormControlLabel
          control={
            <Switch
              checked={applicationProfile.cdrs?.finalCDR}
              onChange={(e: ChangeEvent<HTMLInputElement>) => handleSwitchChange(e, setApplicationProfile)}
              name="cdrs.finalCDR"
            />
          }
          label="Final CDR"
        />
        <FormControlLabel
          control={
            <Switch
              checked={applicationProfile.cdrs?.receiptCDR}
              onChange={(e: ChangeEvent<HTMLInputElement>) => handleSwitchChange(e, setApplicationProfile)}
              name="cdrs.receiptCDR"
            />
          }
          label="Receipt CDR"
        />
        <FormControlLabel
          control={
            <Switch
              checked={applicationProfile.cdrs?.auxiliaryServiceCDR}
              onChange={(e: ChangeEvent<HTMLInputElement>) => handleSwitchChange(e, setApplicationProfile)}
              name="cdrs.auxiliaryServiceCDR"
            />
          }
          label="Auxiliary Service CDR"
        />
      </AccordionDetails>
    </Accordion>
  );
};

export default CDRsAccordion;
