import ToggleOffIcon from "@mui/icons-material/ToggleOff";
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Autocomplete,
  Button,
  Paper,
  TextField,
  Typography,
  AutocompleteChangeReason,
  AutocompleteInputChangeReason
} from "@mui/material";
import React, { useEffect, useState, MouseEvent, ChangeEvent, SyntheticEvent } from "react";
import { SmscNumberListsService } from "@pnmui/common/services/smsc/numberListsService";
import { handleChange } from "./common";
import { ApplicationProfile, NumberList } from "../types";

interface NumberScreeningSettings {
  enabled?: boolean;
  originNumberList?: string | number;
  destinationNumberList?: string | number;
  rejectOnOriginMatch?: boolean;
  rejectOnDestinationMatch?: boolean;
}

interface NumberScreeningAccordionProps {
  applicationProfile: ApplicationProfile;
  setApplicationProfile: React.Dispatch<React.SetStateAction<ApplicationProfile>>;
  numberLists: NumberList[];
  setNumberLists: React.Dispatch<React.SetStateAction<NumberList[]>>;
  errors: Record<string, any>;
  setErrors: React.Dispatch<React.SetStateAction<Record<string, any>>>;
}

const NumberScreeningAccordion: React.FC<NumberScreeningAccordionProps> = ({
  applicationProfile,
  setApplicationProfile,
  numberLists,
  setNumberLists,
  errors,
  setErrors
}) => {
  const [expanded, setExpanded] = useState<boolean>(false);
  const [inputValues, setInputValues] = useState<Record<string, string>>({});
  const [selectedNumberList, setSelectedNumberList] = useState<Record<string, NumberList | null>>({});

  useEffect(() => {
    setExpanded(applicationProfile.numberScreening?.enabled || false);
  }, [applicationProfile.numberScreening?.enabled]);

  const handleToggle = (e: MouseEvent): void => {
    e.stopPropagation();
    const newEnabled = !applicationProfile.numberScreening?.enabled;
    setApplicationProfile((prev) => ({
      ...prev,
      numberScreening: {
        ...prev.numberScreening,
        enabled: newEnabled,
      },
    }));
    setExpanded(newEnabled);

    // Clear errors when disabled
    if (!newEnabled && setErrors) {
      setErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors.numberScreening;
        return newErrors;
      });
    }
  };

  const handleInputChange = (fieldName: string, newValue: string): void => {
    setInputValues((prev) => ({
      ...prev,
      [fieldName]: newValue,
    }));
  };

  const addNewNumberList = async (fieldName: string): Promise<void> => {
    const newName = inputValues[fieldName];
    if (!newName) return;
    try {
      const newList = { name: newName };
      const savedList = await SmscNumberListsService.saveNumberListWithName(
        newList
      );

      // Update the number lists
      setNumberLists((prev) => [...prev, savedList]);

      // Set the newly added list as the selected value
      setApplicationProfile((prev) => ({
        ...prev,
        numberScreening: {
          ...prev.numberScreening,
          [fieldName]: savedList.id,
        },
      }));

      setSelectedNumberList((prev) => ({
        ...prev,
        [fieldName]: savedList,
      }));

      handleInputChange(fieldName, ""); // Clear the input field
    } catch (error: any) {
      console.error("Error adding new number list:", error);
    }
  };

  const renderNumberListAutocomplete = (fieldName: string, label: string): JSX.Element => (
    <Autocomplete
      options={numberLists}
      getOptionLabel={(option) => option.name || ""}
      value={
        selectedNumberList[fieldName] ||
        numberLists.find(
          (list) =>
            list.id ===
            (applicationProfile.numberScreening
              ? applicationProfile.numberScreening[fieldName]
              : null)
        ) ||
        null
      }
      inputValue={inputValues[fieldName] || ""}
      onInputChange={(_, newInputValue) =>
        handleInputChange(fieldName, newInputValue)
      }
      onChange={(_e: SyntheticEvent, value: NumberList | null) => {
        setSelectedNumberList((prev) => ({
          ...prev,
          [fieldName]: value,
        }));
        handleChange(
          {
            target: {
              name: `numberScreening.${fieldName}`,
              value: value ? value.id : null,
            },
          },
          setApplicationProfile
        );
      }}
      renderInput={(params) => (
        <TextField
          {...params}
          label={label}
          error={Boolean(errors?.numberScreening?.[fieldName])}
          helperText={errors?.numberScreening?.[fieldName]}
        />
      )}
      PaperComponent={(props) => (
        <Paper {...props}>
          {props.children}
          <Button
            color="primary"
            fullWidth
            disabled={
              !inputValues[fieldName] ||
              numberLists.some(
                (list) =>
                  list.name.toLowerCase() === inputValues[fieldName].toLowerCase()
              )
            }
            onMouseDown={(_e: MouseEvent) => {
              addNewNumberList(fieldName);
            }}
            sx={{ justifyContent: "flex-start", pl: 2 }}
          >
            + Add New
          </Button>
        </Paper>
      )}
    />
  );

  return (
    <Accordion
      style={{ marginTop: "1em", borderTop: "none" }}
      expanded={expanded}
    >
      <AccordionSummary
        expandIcon={
          <ToggleOffIcon
            style={{
              color: applicationProfile.numberScreening?.enabled
                ? "#3f51b5"
                : "gray",
              fontSize: "3em",
            }}
            onClick={handleToggle}
          />
        }
      >
        <Typography className="accordion-title">Enable Number Screening</Typography>
      </AccordionSummary>
      <AccordionDetails
        className="flex-container"
        style={{ justifyContent: "start", flexWrap: "wrap" }}
      >
        {renderNumberListAutocomplete("aNumberAllowList", "A Number Allowlist")}
        {renderNumberListAutocomplete("aNumberBlockList", "A Number BlockList")}
        {renderNumberListAutocomplete("bNumberAllowList", "B Number Allowlist")}
        {renderNumberListAutocomplete("bNumberBlockList", "B Number BlockList")}
        {renderNumberListAutocomplete("vlrAllowList", "VLR Allowlist")}
        {renderNumberListAutocomplete("vlrBlockList", "VLR BlockList")}
      </AccordionDetails>
    </Accordion>
  );
};

export default NumberScreeningAccordion;



