import ToggleOffIcon from "@mui/icons-material/ToggleOff";
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  FormControlLabel,
  MenuItem,
  Switch,
  Typography,
} from "@mui/material";
import React, { useEffect, useState, MouseEvent, ChangeEvent } from "react";
import { SelectValidator } from "react-material-ui-form-validator";
import { handleChange, handleSwitchChange } from "./common";
import { ApplicationProfile, EnumerationOption } from "../types";

interface ChargingSettings {
  enabled?: boolean;
  chargingType?: string;
  chargingDestination?: string;
  chargingOrigin?: string;
  chargingAmount?: string;
}

interface ChargingAccordionProps {
  applicationProfile: ApplicationProfile;
  setApplicationProfile: React.Dispatch<React.SetStateAction<ApplicationProfile>>;
  enumerations: Record<string, EnumerationOption[]>;
  errors: Record<string, any>;
  setErrors: React.Dispatch<React.SetStateAction<Record<string, any>>>;
}

const ChargingAccordion: React.FC<ChargingAccordionProps> = ({
  applicationProfile,
  setApplicationProfile,
  enumerations,
  errors,
  setErrors
}) => {
  const [expanded, setExpanded] = useState<boolean>(false);

  useEffect(() => {
    setExpanded(applicationProfile.charging?.enabled || false);
  }, [applicationProfile.charging?.enabled]);

  const handleToggle = (e: MouseEvent): void => {
    e.stopPropagation();
    const newEnabled = !applicationProfile.charging?.enabled;
    setApplicationProfile((prev) => ({
      ...prev,
      charging: {
        ...prev.charging,
        enabled: newEnabled,
      },
    }));
    setExpanded(newEnabled);

    // Clear errors when disabled
    if (!newEnabled && setErrors) {
      setErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors.charging;
        return newErrors;
      });
    }
  };

  const handleChargingChange = (e: ChangeEvent<HTMLInputElement>): void => {
    handleChange(e, setApplicationProfile);
  };

  return (
    <Accordion
      style={{ marginTop: "1em", borderTop: "none" }}
      expanded={expanded}
    >
      <AccordionSummary
        expandIcon={
          <ToggleOffIcon
            style={{
              color: expanded ? "#3f51b5" : "gray",
              fontSize: "3em",
            }}
            onClick={handleToggle}
          />
        }
      >
        <Typography className="accordion-title">Enable Charging</Typography>
      </AccordionSummary>
      <AccordionDetails
        className="flex-container"
        style={{ justifyContent: "start" }}
      >
        <FormControlLabel
          control={
            <Switch
              checked={applicationProfile.charging?.changeOriginatingSubscriber}
              onChange={(e: ChangeEvent<HTMLInputElement>) => handleSwitchChange(e, setApplicationProfile)}
              name="charging.changeOriginatingSubscriber"
            />
          }
          label="Charge Originating Subscriber"
        />
        <div className="flex-container" style={{ justifyContent: "start" }}>
          <SelectValidator
            label="When to Charge"
            onChange={handleChargingChange}
            name="charging.whenToCharge"
            value={
              applicationProfile.charging?.whenToCharge?.id ||
              applicationProfile.charging?.whenToCharge ||
              ""
            }
            validators={applicationProfile.charging?.enabled ? ["required"] : []}
            errorMessages={["When to charge is required"]}
            error={Boolean(errors?.charging?.whenToCharge)}
            helperText={errors?.charging?.whenToCharge}
          >
            {(enumerations?.chargingStrategy || []).map((option) => (
              <MenuItem key={option.value} value={option.value}>
                {option.displayText}
              </MenuItem>
            ))}
          </SelectValidator>
        </div>
      </AccordionDetails>
    </Accordion>
  );
};

export default ChargingAccordion;
