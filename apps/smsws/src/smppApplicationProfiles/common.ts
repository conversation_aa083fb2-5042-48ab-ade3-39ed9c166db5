import { ChangeEvent, Dispatch, SetStateAction } from 'react';

/**
 * <PERSON>les input change events for form fields
 * @param e - The change event from the input field
 * @param setApplicationProfile - State setter function for the application profile
 */
export const handleChange = (
  e: ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>,
  setApplicationProfile: Dispatch<SetStateAction<any>>
): void => {
  const { name, value } = e.target;
  if (name.indexOf(".") > -1) {
    const [parent, child] = name.split(".");
    setApplicationProfile((prev: any) => ({
      ...prev,
      [parent]: {
        ...prev[parent],
        [child]: value,
      },
    }));
  } else {
    setApplicationProfile((prev: any) => ({
      ...prev,
      [name]: value,
    }));
  }
};

/**
 * Handles switch/checkbox change events for form fields
 * @param e - The change event from the switch/checkbox
 * @param setApplicationProfile - State setter function for the application profile
 */
export const handleSwitchChange = (
  e: ChangeEvent<HTMLInputElement>,
  setApplicationProfile: Dispatch<SetStateAction<any>>
): void => {
  const { name, checked } = e.target;
  if (name.indexOf(".") > -1) {
    const [parent, child] = name.split(".");
    setApplicationProfile((prev: any) => ({
      ...prev,
      [parent]: {
        ...prev[parent],
        [child]: checked,
      },
    }));
  } else {
    setApplicationProfile((prev: any) => ({
      ...prev,
      [name]: checked,
    }));
  }
};
