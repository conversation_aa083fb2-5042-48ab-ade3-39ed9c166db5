.flex-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
}

.flex-item {
    flex: 1 0 auto;
    margin: 0.5em;
}

.accordion-title {
    font-weight: bold;
}

.tango-form [class^="MuiFormControl"] {
    margin: 1em !important;
}

.MuiAutocomplete-root [class^="MuiFormControl"] {
    margin-left: 0 !important;
    overflow: auto;
    padding: 0;
}

.MuiTextField-root, .MuiAutocomplete-root {
    min-width: 15em !important;
}

/* Specific styling for name fields to be normal size */
.MuiTextField-root:has(input[name="name"]) {
    min-width: 15em !important;
    width: 15em;
}

.MuiTextField-root:has(input[type="number"]) {
    min-width: 15vw !important;
    width: 15vw;
}

.MuiTextField-root:has(input[type="number"]).smaller-number-input {
    min-width: 7em !important;
    width: 7em;
}

.k-grid .k-grid-header {
    padding-right: 0 !important;
}

.k-grid-content {
    overflow: auto;
}

.k-toolbar {
    padding: 0 !important;
}

/* Additional KendoReact Grid styling */
.k-grid {
    border-width: 1px;
    border-style: solid;
    border-color: rgba(0, 0, 0, 0.12);
}

.k-grid-toolbar {
    display: flex;
    justify-content: flex-end;
    padding: 8px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.12);
}

.k-grid-header th {
    font-weight: bold;
    background-color: #f5f5f5;
    padding: 8px;
}

.k-grid td {
    padding: 8px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.12);
}

.tango-form [class^="MuiFormControl"] {
    margin: 0;
}

.small-input.MuiTextField-root:has(input[type="number"]) {
    min-width: auto !important;
    width: 5em;
}

.MuiFormControl-fullWidth, .MuiFormControl-fullWidth > div {
    width: 100% !important;
    min-width: 10em !important;
    max-width: 100% !important;
}

.MuiAutocomplete-root {
    margin: -1.5em 1em 0 1em;
}

.k-grid-content .MuiAutocomplete-root {
    margin: 0;
}

.k-grid-content .MuiInputBase-root {
    max-width: 15em;
    width: 75% !important;
}

.MuiAutocomplete-root .MuiInputBase-root {
    padding: 0;
    width: 10em;
    overflow-x: visible;
}

.MuiAutocomplete-root label {
    position: relative;
    top: 1.5em;
}

/*.MuiAutocomplete-root .MuiFormControl-fullWidth,  .MuiAutocomplete-root .MuiFormControl-fullWidth div {*/
/*    width: 18em !important;*/
/*    min-width: 18em !important;*/
/*    max-width: none !important;*/
/*}*/

/*.small-input, .small-input input, .small-input input[style], .small-input fieldset, .small-input .MuiInputBase-sizeSmall[style] {*/
/*    width: 3em !important;*/
/*    max-width: 3em !important;*/
/*    min-width: 3em !important;*/
/*}*/

.MuiAccordionSummary-root {
    justify-content: flex-start !important;
}

.MuiAccordionSummary-content {
    flex-direction: row-reverse !important;
    margin: 0 !important;
    flex-grow: 0 !important;
}

.MuiAccordionSummary-expandIconWrapper {
    margin-right: 1em !important;
    margin-left: 1.5em;
}

/* Grid button styling */
.k-button.k-grid-edit-command,
.k-button.k-grid-delete-command,
.k-button.k-grid-save-command,
.k-button.k-grid-cancel-command,
.k-primary.k-button.k-grid-edit-command {
    padding-left: 1.2em !important;
    padding-right: 1.2em !important;
    margin: 0.2em;
    min-width: 5em;
    text-align: center;
}

/* Additional Grid styling for TypeScript version */
.k-grid {
    display: block !important;
    width: 100% !important;
}

.k-grid-header {
    display: block !important;
    width: 100% !important;
}

.k-grid-header-wrap {
    display: block !important;
    width: 100% !important;
}

.k-grid-content {
    display: block !important;
    width: 100% !important;
}

.k-grid-table {
    width: 100% !important;
}

/* Fix for Grid in edit mode */
.k-grid td .MuiTextField-root {
    margin: 0 !important;
    width: 100% !important;
}

.k-grid td .MuiInputBase-root {
    width: 100% !important;
    max-width: none !important;
}
