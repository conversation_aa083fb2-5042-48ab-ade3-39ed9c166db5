import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  <PERSON>ton,
  Card,
  CardActions,
  CardContent,
  MenuItem,
  Typography,
} from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import {
  BooleanYesNoCell,
  CommandCell,
  ErrorsDisplay,
  InputCell,
  SelectCell,
} from "@pnmui/common/components";
import { $i18n } from "@pnmui/common/services/propertiesService";
import { SmscNumberAnalysisService } from "@pnmui/common/services/smsc/numberAnalysisService";
import { SmscNumberListsService } from "@pnmui/common/services/smsc/numberListsService";
import { GridItemChangeEvent, GridToolbar } from "@progress/kendo-react-grid";
import { ChangeEvent, useEffect, useRef, useState } from "react";
import {
  SelectValidator,
  TextValidator,
  ValidatorForm,
} from "react-material-ui-form-validator";
import { useNavigate } from "react-router-dom";
import { toast } from "react-toastify";
import { Subscription } from "rxjs";
import { tap } from "rxjs/operators";
import "../common.css";
import "./numberAnalysis.css";
import "../stripAndReplace/stripAndReplaceFormDialog.css";
import { Column, Grid } from "../components/KendoGridWrapper";
import {
  ChargingMethod,
  NumberAnalysis,
  NumberAnalysisANumberWhiteList,
  NumberAnalysisChargingOverride,
  NumberList,
} from "../types";

interface FormErrors {
  chargingMethodReloadHost?: string;
  anumberWhiteLists?: Record<string | number, Record<string, string>>;
  chargingOverrideList?: Record<string | number, Record<string, string>>;
}

const SmscNumberAnalysisForm = () => {
  const navigate = useNavigate();
  const formRef = useRef<any>(null);
  const [i18n, setI18n] = useState<Record<string, string>>({});
  const [errorResponse, setErrorResponse] = useState<any>(null);
  const [numberLists, setNumberLists] = useState<NumberList[]>([]);
  const [chargingMethods, setChargingMethods] = useState<ChargingMethod[]>([]);
  const [errors, setErrors] = useState<FormErrors>({});
  const [numberAnalysis, setNumberAnalysis] = useState<NumberAnalysis>({
    id: null,
    chargingMethodReloadHost: "",
    anumberWhiteLists: [],
    chargingOverrideList: [],
    anumberBlackList: null,
    bnumberBlackList: null,
    bnumberWhiteList: null,
  });
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isReloading, setIsReloading] = useState<boolean>(false);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);

  /**
   * Loads all data from the server (number analysis, charging methods, number lists)
   * @param showLoading Whether to show the loading indicator
   */
  const loadAllData = (showLoading = true) => {
    if (showLoading) {
      setIsLoading(true);
    }

    // Load all data in parallel
    return Promise.all([
      // Load charging methods directly from the charging methods endpoint
      SmscNumberAnalysisService.getChargingMethods(),
      // Load number lists
      SmscNumberListsService.getNumberLists(),
      // Try to load number analysis with ID 1 (single configuration)
      SmscNumberAnalysisService.getNumberAnalysisById(1).catch(() => null)
    ])
      .then(([chargingMethodsData, lists, analysisData]) => {
        // Process charging methods
        if (chargingMethodsData) {
          setChargingMethods(chargingMethodsData);
        }

        // Process number lists
        if (lists) {
          setNumberLists(lists);
        }

        // Process number analysis data
        if (analysisData) {
          // Map server response to UI model
          const processedData = {
            ...analysisData,
            anumberWhiteLists: (analysisData.anumberWhiteLists || []).map(item => {
              // Map anumberWhiteList to allowList if allowList is not present
              if (item.anumberWhiteList && !item.allowList) {
                return {
                  ...item,
                  allowList: item.anumberWhiteList
                };
              }
              return item;
            })
          };

          console.log("Processed number analysis data:", processedData);
          setNumberAnalysis(processedData);
        }

        return { chargingMethodsData, lists, analysisData };
      })
      .catch((error) => {
        toast.error("Failed to load data: " + error.message);
        return null;
      })
      .finally(() => {
        if (showLoading) {
          setIsLoading(false);
        }
      });
  };

  useEffect(() => {
    const subscription: Subscription = $i18n
      .pipe(tap())
      .subscribe((i18NProps) => {
        if (i18NProps) {
          setI18n(i18NProps);
        }
      });

    // Load all data on component mount
    loadAllData();

    return () => subscription.unsubscribe();
  }, []);

  // Effect to handle grid layout recalculation when data changes
  useEffect(() => {
    // Only run if we have data and component is mounted
    if (numberAnalysis.anumberWhiteLists?.length > 0 || numberAnalysis.chargingOverrideList?.length > 0) {
      // Create a sequence of layout recalculations at different times
      // to ensure the grid layout is properly updated
      const timeoutIds: NodeJS.Timeout[] = [];

      // Immediate recalculation
      timeoutIds.push(setTimeout(() => forceGridLayoutRecalculation(), 0));

      // After DOM update (short delay)
      timeoutIds.push(setTimeout(() => forceGridLayoutRecalculation(), 50));

      // After React has fully processed the update (medium delay)
      timeoutIds.push(setTimeout(() => forceGridLayoutRecalculation(), 150));

      // Final check after everything should be settled (longer delay)
      timeoutIds.push(setTimeout(() => forceGridLayoutRecalculation(), 300));

      // Cleanup function to clear all timeouts
      return () => timeoutIds.forEach(id => clearTimeout(id));
    }
  }, [numberAnalysis.anumberWhiteLists, numberAnalysis.chargingOverrideList]);

  const handleChange = (e: ChangeEvent<HTMLInputElement>): void => {
    const { name, value } = e.target;
    setNumberAnalysis({ ...numberAnalysis, [name]: value });
  };

  const handleSelectChange = (e: ChangeEvent<{ name?: string; value: unknown }>): void => {
    const { name, value } = e.target;
    if (name) {
      setNumberAnalysis({ ...numberAnalysis, [name]: value });
    }
  };

  // This function is intentionally removed as it was copied from another component
  // and is not needed in the Number Analysis form

  /**
   * Validates the form and auto-fixes missing prepaid lists
   * @returns An object containing validation errors, if any
   */
  const validateAndAutoFix = (): { formErrors: FormErrors; updatedData: NumberAnalysis | null } => {
    // Clear any previous errors
    const formErrors: FormErrors = {};

    // Create a copy of the numberAnalysis to work with
    const updatedNumberAnalysis = { ...numberAnalysis };
    let needsUpdate = false;

    // Auto-fix A-Number White Lists
    if (updatedNumberAnalysis.anumberWhiteLists && updatedNumberAnalysis.anumberWhiteLists.length > 0) {
      const updatedWhiteLists = updatedNumberAnalysis.anumberWhiteLists.map(item => {
        if (!item) return item;

        const updatedItem = { ...item };
        let itemNeedsUpdate = false;

        // Auto-set prepaidList if missing
        if (!updatedItem.prepaidList) {
          const prepaidListId = findPrepaidListForChargingMethod(updatedItem.chargingMethod);
          if (prepaidListId) {
            console.log(`Auto-setting prepaid list to ${prepaidListId} for A-Number White List item ${updatedItem.id} before validation`);
            updatedItem.prepaidList = prepaidListId;
            itemNeedsUpdate = true;
          }
        }

        if (itemNeedsUpdate) {
          needsUpdate = true;
          return updatedItem;
        }

        return item;
      });

      if (needsUpdate) {
        updatedNumberAnalysis.anumberWhiteLists = updatedWhiteLists;
      }
    }

    // Auto-fix Charging Override List
    if (updatedNumberAnalysis.chargingOverrideList && updatedNumberAnalysis.chargingOverrideList.length > 0) {
      const updatedOverrideList = updatedNumberAnalysis.chargingOverrideList.map(item => {
        if (!item) return item;

        const updatedItem = { ...item };
        let itemNeedsUpdate = false;

        // Auto-set prepaidList if missing
        if (!updatedItem.prepaidList) {
          const prepaidListId = findPrepaidListForChargingMethod(updatedItem.chargingMethod);
          if (prepaidListId) {
            console.log(`Auto-setting prepaid list to ${prepaidListId} for Charging Override item ${updatedItem.id} before validation`);
            updatedItem.prepaidList = prepaidListId;
            itemNeedsUpdate = true;
          }
        }

        if (itemNeedsUpdate) {
          needsUpdate = true;
          return updatedItem;
        }

        return item;
      });

      if (needsUpdate) {
        updatedNumberAnalysis.chargingOverrideList = updatedOverrideList;
      }
    }

    // If we made updates, return the updated data
    if (needsUpdate) {
      console.log("Auto-fixed form data before validation:", JSON.stringify(updatedNumberAnalysis, null, 2));
      return { formErrors, updatedData: updatedNumberAnalysis };
    }

    // Validate the form using the existing validateForm function
    const validationErrors = validateForm();

    return { formErrors: validationErrors, updatedData: null };
  };

  /**
   * Handles the form submission
   */
  const handleSubmit = (): void => {
    console.log("Form submission started");

    // Clear any previous errors
    setErrors({});
    setErrorResponse(null);

    // Validate and auto-fix the form
    const { formErrors, updatedData } = validateAndAutoFix();

    // If we have updated data, update the state and submit
    if (updatedData) {
      // Update the state
      setNumberAnalysis(updatedData);

      // Submit the form after a short delay to allow state to update
      setTimeout(() => {
        console.log("Auto-submitting form after data fix");
        submitFormData(updatedData);
      }, 500);

      return;
    }

    // If we have validation errors, display them
    if (Object.keys(formErrors).length > 0) {
      setErrors(formErrors);
      console.log("Form validation failed:", formErrors);
      return;
    }

    console.log("Form validation passed");

    // Log the current state for debugging
    console.log("Current form state:", JSON.stringify(numberAnalysis, null, 2));

    // Submit the form
    submitFormData(numberAnalysis);
  };

  /**
   * Submits the form data to the server
   * @param formData The form data to submit
   */
  const submitFormData = (formData: NumberAnalysis): void => {
    console.log("Submitting form data:", JSON.stringify(formData, null, 2));

    // Prepare data for submission
    const dataToSubmit = {
      ...formData,
      // Always set ID to 1 (single configuration)
      id: 1,
      // Ensure proper formatting of data for the server
      anumberWhiteLists: (formData.anumberWhiteLists || []).map(item => {
        if (!item) return null;

        // Extract the chargingMethod ID if it's an object
        let chargingMethodId: string | null = null;
        let prepaidListId: string | number | null = null;

        // Process charging method
        if (item.chargingMethod !== null && item.chargingMethod !== undefined) {
          if (typeof item.chargingMethod === 'object') {
            // Extract ID from object and convert to string
            const id = (item.chargingMethod as any).id || (item.chargingMethod as any).value;
            chargingMethodId = id !== null && id !== undefined ? String(id) : null;
            console.log(`Extracted charging method ID ${chargingMethodId} from object:`, item.chargingMethod);
          } else {
            // Already a primitive value, convert to string
            chargingMethodId = String(item.chargingMethod);
            console.log(`Using primitive charging method ID: ${chargingMethodId}`);
          }
        }

        // Process prepaid list
        if (item.prepaidList !== null && item.prepaidList !== undefined) {
          if (typeof item.prepaidList === 'object') {
            // Extract ID from object
            const id = (item.prepaidList as any).id || (item.prepaidList as any).value;
            prepaidListId = id !== null && id !== undefined ? id : null;
            console.log(`Extracted prepaid list ID ${prepaidListId} from object:`, item.prepaidList);
          } else {
            // Already a primitive value
            prepaidListId = item.prepaidList;
            console.log(`Using primitive prepaid list ID: ${prepaidListId}`);
          }
        }

        // Map allowList to anumberWhiteList for server compatibility
        let anumberWhiteList = null;
        if (item.allowList !== null && item.allowList !== undefined) {
          if (typeof item.allowList === 'object') {
            // Extract ID from object
            const id = (item.allowList as any).id || (item.allowList as any).value;
            anumberWhiteList = id !== null && id !== undefined ? id : null;
            console.log(`Extracted allow list ID ${anumberWhiteList} from object:`, item.allowList);
          } else {
            // Already a primitive value
            anumberWhiteList = item.allowList;
            console.log(`Using primitive allow list ID: ${anumberWhiteList}`);
          }
        } else if (item.anumberWhiteList) {
          anumberWhiteList = item.anumberWhiteList;
        }

        return {
          id: item.id, // Keep the ID for existing items
          inEdit: undefined, // Remove UI-only properties
          allowList: undefined, // Remove UI-only property
          anumberWhiteList: anumberWhiteList, // Ensure anumberWhiteList is set
          chargingMethod: chargingMethodId, // Ensure chargingMethod is sent as a string ID
          prepaidList: prepaidListId, // Ensure prepaidList is sent as a primitive ID
          chargingOverrideEnabled: item.chargingOverrideEnabled
        };
      }).filter(Boolean), // Remove null entries

      chargingOverrideList: (formData.chargingOverrideList || []).map(item => {
        if (!item) return null;

        // Extract the chargingMethod ID if it's an object
        let chargingMethodId: string | null = null;
        let prepaidListId: string | number | null = null;

        // Process charging method
        if (item.chargingMethod !== null && item.chargingMethod !== undefined) {
          if (typeof item.chargingMethod === 'object') {
            // Extract ID from object and convert to string
            const id = (item.chargingMethod as any).id || (item.chargingMethod as any).value;
            chargingMethodId = id !== null && id !== undefined ? String(id) : null;
            console.log(`Extracted charging override method ID ${chargingMethodId} from object:`, item.chargingMethod);
          } else {
            // Already a primitive value, convert to string
            chargingMethodId = String(item.chargingMethod);
            console.log(`Using primitive charging override method ID: ${chargingMethodId}`);
          }
        }

        // Process prepaid list
        if (item.prepaidList !== null && item.prepaidList !== undefined) {
          if (typeof item.prepaidList === 'object') {
            // Extract ID from object
            const id = (item.prepaidList as any).id || (item.prepaidList as any).value;
            prepaidListId = id !== null && id !== undefined ? id : null;
            console.log(`Extracted charging override prepaid list ID ${prepaidListId} from object:`, item.prepaidList);
          } else {
            // Already a primitive value
            prepaidListId = item.prepaidList;
            console.log(`Using primitive charging override prepaid list ID: ${prepaidListId}`);
          }
        }

        return {
          id: item.id, // Keep the ID for existing items
          inEdit: undefined, // Remove UI-only properties
          bnumberPrefix: item.bnumberPrefix,
          chargingMethod: chargingMethodId, // Ensure chargingMethod is sent as a string ID
          prepaidList: prepaidListId // Ensure prepaidList is sent as a primitive ID
        };
      }).filter(Boolean) // Remove null entries
    };

    // Submit the form
    setIsSubmitting(true);
    console.log("Submitting data to server:", JSON.stringify(dataToSubmit, null, 2));

    // Ensure all required fields are present and properly formatted
    try {
      // Validate that all required fields are present
      if (!dataToSubmit.chargingMethodReloadHost) {
        throw new Error("Host is required");
      }

      // Validate that anumberWhiteLists is an array
      if (!Array.isArray(dataToSubmit.anumberWhiteLists)) {
        throw new Error("A-Number White Lists must be an array");
      }

      // Validate that chargingOverrideList is an array
      if (!Array.isArray(dataToSubmit.chargingOverrideList)) {
        throw new Error("Charging Override List must be an array");
      }

      // Validate and auto-fix anumberWhiteLists
      dataToSubmit.anumberWhiteLists = dataToSubmit.anumberWhiteLists.map((item, index) => {
        if (!item) return null;

        // Create a copy of the item to modify
        const updatedItem = { ...item };

        // Check for required fields
        if (!updatedItem.anumberWhiteList) {
          throw new Error(`A-Number White List item ${index + 1} is missing the anumberWhiteList field`);
        }

        if (!updatedItem.chargingMethod) {
          throw new Error(`A-Number White List item ${index + 1} is missing the chargingMethod field`);
        }

        // Auto-set prepaidList if missing
        if (!updatedItem.prepaidList) {
          const prepaidListId = findPrepaidListForChargingMethod(updatedItem.chargingMethod);
          if (prepaidListId) {
            console.log(`Auto-setting prepaid list to ${prepaidListId} for A-Number White List item ${updatedItem.id} during submission`);
            updatedItem.prepaidList = prepaidListId;
          } else {
            // If we can't find a prepaid list, use the first available one as a fallback
            if (numberLists.length > 0) {
              updatedItem.prepaidList = numberLists[0].id;
              console.log(`Using fallback prepaid list ${updatedItem.prepaidList} for A-Number White List item ${updatedItem.id}`);
            } else {
              throw new Error(`A-Number White List item ${index + 1} is missing the prepaidList field and no fallback is available`);
            }
          }
        }

        return updatedItem;
      }).filter(Boolean);

      // Validate and auto-fix chargingOverrideList
      dataToSubmit.chargingOverrideList = dataToSubmit.chargingOverrideList.map((item, index) => {
        if (!item) return null;

        // Create a copy of the item to modify
        const updatedItem = { ...item };

        // Check for required fields
        if (!updatedItem.bnumberPrefix) {
          throw new Error(`Charging Override item ${index + 1} is missing the bnumberPrefix field`);
        }

        if (!updatedItem.chargingMethod) {
          throw new Error(`Charging Override item ${index + 1} is missing the chargingMethod field`);
        }

        // Auto-set prepaidList if missing
        if (!updatedItem.prepaidList) {
          const prepaidListId = findPrepaidListForChargingMethod(updatedItem.chargingMethod);
          if (prepaidListId) {
            console.log(`Auto-setting prepaid list to ${prepaidListId} for Charging Override item ${updatedItem.id} during submission`);
            updatedItem.prepaidList = prepaidListId;
          } else {
            // If we can't find a prepaid list, use the first available one as a fallback
            if (numberLists.length > 0) {
              updatedItem.prepaidList = numberLists[0].id;
              console.log(`Using fallback prepaid list ${updatedItem.prepaidList} for Charging Override item ${updatedItem.id}`);
            } else {
              throw new Error(`Charging Override item ${index + 1} is missing the prepaidList field and no fallback is available`);
            }
          }
        }

        return updatedItem;
      }).filter(Boolean);

      // Call the service to save the data
      SmscNumberAnalysisService.saveNumberAnalysis(dataToSubmit)
        .then((response) => {
          console.log("Server response:", response);
          toast.success(
            i18n["smsws.numberAnalysis.form.saveSuccess"] ||
              "Number Analysis saved successfully"
          );

          // Reload all data from the server to ensure proper display
          console.log("Reloading data after successful submission");
          return loadAllData(false);
        })
        .catch((error) => {
          console.error("Error submitting form:", error);
          console.log("Error details:", JSON.stringify(error, null, 2));

          // Check for specific validation errors
          if (error.response?.data?.errors) {
            const serverErrors = error.response.data.errors;
            console.log("Server validation errors:", serverErrors);

            // Handle specific error for charging override required
            if (serverErrors.anumberWhiteLists === "smsws.validation.number.analysis.charging.override.required.when.enabled") {
              const formErrors: FormErrors = {
                anumberWhiteLists: {
                  general: {
                    chargingOverride: "At least one Charging Override entry is required when Charging Override is enabled"
                  }
                }
              };
              setErrors(formErrors);
              toast.error("At least one Charging Override entry is required when Charging Override is enabled");
            } else {
              // Handle other errors
              toast.error(
                (i18n["smsws.numberAnalysis.form.saveError"] ||
                  "Failed to save Number Analysis: ") + error.message
              );
              setErrorResponse(error);
            }
          } else {
            // Generic error handling
            toast.error(
              (i18n["smsws.numberAnalysis.form.saveError"] ||
                "Failed to save Number Analysis: ") + error.message
            );
            setErrorResponse(error);
          }
        })
        .finally(() => {
          setIsSubmitting(false);
        });
    } catch (error) {
      console.error("Validation error:", error);
      toast.error(error.message);
      setIsSubmitting(false);
    }
  };

  const validateForm = (): FormErrors => {
    const formErrors: FormErrors = {};
    console.log("Validating form with data:", JSON.stringify(numberAnalysis, null, 2));

    // Create a copy of the numberAnalysis to work with
    let updatedNumberAnalysis = { ...numberAnalysis };
    let needsUpdate = false;

    // Validate required fields
    if (!updatedNumberAnalysis.chargingMethodReloadHost) {
      toast.error("Host is required");
      formErrors.chargingMethodReloadHost = "Host is required";
    }

    // Validate and auto-fix A-Number White Lists
    if (updatedNumberAnalysis.anumberWhiteLists && updatedNumberAnalysis.anumberWhiteLists.length > 0) {
      const anumberWhiteListErrors: Record<string | number, Record<string, string>> = {};
      const updatedWhiteLists = updatedNumberAnalysis.anumberWhiteLists.map((item) => {
        if (!item) return item;

        const itemErrors: Record<string, string> = {};
        let updatedItem = { ...item };
        let itemNeedsUpdate = false;

        // Check allowList - can be object or primitive
        if (!updatedItem.allowList) {
          itemErrors.allowList = "Allow List is required";
        }

        // Check chargingMethod - can be object or primitive
        if (updatedItem.chargingMethod === null || updatedItem.chargingMethod === undefined) {
          itemErrors.chargingMethod = "Charging Method is required";
        } else if (typeof updatedItem.chargingMethod === 'object') {
          // If it's an object, make sure it has an id or value property
          const id = (updatedItem.chargingMethod as any).id || (updatedItem.chargingMethod as any).value;
          if (id === null || id === undefined) {
            itemErrors.chargingMethod = "Invalid Charging Method format";
          }
        }

        // Check prepaidList - can be object or primitive
        if (updatedItem.prepaidList === null || updatedItem.prepaidList === undefined) {
          // Try to auto-set the prepaid list based on the charging method
          if (updatedItem.chargingMethod) {
            const prepaidListId = findPrepaidListForChargingMethod(updatedItem.chargingMethod);
            if (prepaidListId) {
              console.log(`Auto-setting prepaid list to ${prepaidListId} for A-Number White List item ${updatedItem.id}`);
              updatedItem.prepaidList = prepaidListId;
              itemNeedsUpdate = true;
            } else {
              itemErrors.prepaidList = "Prepaid List is required";
            }
          } else {
            itemErrors.prepaidList = "Prepaid List is required";
          }
        } else if (typeof updatedItem.prepaidList === 'object') {
          // If it's an object, make sure it has an id or value property
          const id = (updatedItem.prepaidList as any).id || (updatedItem.prepaidList as any).value;
          if (id === null || id === undefined) {
            itemErrors.prepaidList = "Invalid Prepaid List format";
          }
        }

        if (Object.keys(itemErrors).length > 0) {
          console.log(`Validation errors for A-Number White List item ${updatedItem.id}:`, itemErrors);
          anumberWhiteListErrors[updatedItem.id as string | number] = itemErrors;
        }

        if (itemNeedsUpdate) {
          needsUpdate = true;
        }

        return updatedItem;
      });

      if (needsUpdate) {
        updatedNumberAnalysis = {
          ...updatedNumberAnalysis,
          anumberWhiteLists: updatedWhiteLists
        };
      }

      if (Object.keys(anumberWhiteListErrors).length > 0) {
        formErrors.anumberWhiteLists = anumberWhiteListErrors;
      }
    }

    // Validate and auto-fix Charging Override List
    if (updatedNumberAnalysis.chargingOverrideList && updatedNumberAnalysis.chargingOverrideList.length > 0) {
      const chargingOverrideErrors: Record<string | number, Record<string, string>> = {};
      const updatedOverrideList = updatedNumberAnalysis.chargingOverrideList.map((item) => {
        if (!item) return item;

        const itemErrors: Record<string, string> = {};
        let updatedItem = { ...item };
        let itemNeedsUpdate = false;

        // Check bnumberPrefix
        if (!updatedItem.bnumberPrefix) {
          itemErrors.bnumberPrefix = "B-Number Prefix is required";
        }

        // Check chargingMethod - can be object or primitive
        if (updatedItem.chargingMethod === null || updatedItem.chargingMethod === undefined) {
          itemErrors.chargingMethod = "Charging Method is required";
        } else if (typeof updatedItem.chargingMethod === 'object') {
          // If it's an object, make sure it has an id or value property
          const id = (updatedItem.chargingMethod as any).id || (updatedItem.chargingMethod as any).value;
          if (id === null || id === undefined) {
            itemErrors.chargingMethod = "Invalid Charging Method format";
          }
        }

        // Check prepaidList - can be object or primitive
        if (updatedItem.prepaidList === null || updatedItem.prepaidList === undefined) {
          // Try to auto-set the prepaid list based on the charging method
          if (updatedItem.chargingMethod) {
            const prepaidListId = findPrepaidListForChargingMethod(updatedItem.chargingMethod);
            if (prepaidListId) {
              console.log(`Auto-setting prepaid list to ${prepaidListId} for Charging Override item ${updatedItem.id}`);
              updatedItem.prepaidList = prepaidListId;
              itemNeedsUpdate = true;
            } else {
              itemErrors.prepaidList = "Prepaid List is required";
            }
          } else {
            itemErrors.prepaidList = "Prepaid List is required";
          }
        } else if (typeof updatedItem.prepaidList === 'object') {
          // If it's an object, make sure it has an id or value property
          const id = (updatedItem.prepaidList as any).id || (updatedItem.prepaidList as any).value;
          if (id === null || id === undefined) {
            itemErrors.prepaidList = "Invalid Prepaid List format";
          }
        }

        if (Object.keys(itemErrors).length > 0) {
          console.log(`Validation errors for Charging Override item ${updatedItem.id}:`, itemErrors);
          chargingOverrideErrors[updatedItem.id as string | number] = itemErrors;
        }

        if (itemNeedsUpdate) {
          needsUpdate = true;
        }

        return updatedItem;
      });

      if (needsUpdate) {
        updatedNumberAnalysis = {
          ...updatedNumberAnalysis,
          chargingOverrideList: updatedOverrideList
        };
      }

      if (Object.keys(chargingOverrideErrors).length > 0) {
        formErrors.chargingOverrideList = chargingOverrideErrors;
      }
    }

    // Check if any A-Number White List has charging override enabled but no charging override entries exist
    const hasChargingOverrideEnabled = updatedNumberAnalysis.anumberWhiteLists?.some(item => item.chargingOverrideEnabled);
    const hasChargingOverrideEntries = updatedNumberAnalysis.chargingOverrideList?.length > 0;

    if (hasChargingOverrideEnabled && !hasChargingOverrideEntries) {
      toast.error("At least one Charging Override entry is required when Charging Override is enabled");
      formErrors.anumberWhiteLists = {
        ...(formErrors.anumberWhiteLists || {}),
        general: {
          chargingOverride: "At least one Charging Override entry is required when Charging Override is enabled"
        }
      };
    }

    // If we made updates to the numberAnalysis, update the state
    if (needsUpdate) {
      console.log("Auto-fixed form data:", JSON.stringify(updatedNumberAnalysis, null, 2));
      setNumberAnalysis(updatedNumberAnalysis);
    }

    console.log("Validation result:", Object.keys(formErrors).length > 0 ? "Failed" : "Passed", formErrors);
    return formErrors;
  };

  const generateUniqueId = (): number => {
    return Math.floor(Math.random() * 1000);
  };

  // Reference to force grid refresh
  const gridRefreshTimeout = useRef<NodeJS.Timeout | null>(null);

  // Function to force grid layout recalculation
  const forceGridLayoutRecalculation = (): void => {
    // Clear any existing timeout
    if (gridRefreshTimeout.current) {
      clearTimeout(gridRefreshTimeout.current);
    }

    // More aggressive approach to force layout recalculation
    const recalculateGridLayout = () => {
      // Target all grids, but give special attention to the charging override grid
      const grids = document.querySelectorAll('.k-grid');
      const chargingOverrideGrid = document.querySelector('.charging-override-grid');

      // Process all grids
      grids.forEach(grid => {
        // Step 1: Add force-recalculate class
        grid.classList.add('force-recalculate');

        // Step 2: Force reflow by accessing offsetHeight
        void (grid as HTMLElement).offsetHeight;

        // Step 3: Force column width recalculation
        const headers = grid.querySelectorAll('.k-grid-header th');
        const cells = grid.querySelectorAll('.k-grid-content td');

        // Force reflow on headers and cells
        headers.forEach((header, index) => {
          const headerWidth = header.getBoundingClientRect().width;

          // Find corresponding cells for this column
          const columnCells = grid.querySelectorAll(`.k-grid-content td:nth-child(${index + 1})`);
          columnCells.forEach(cell => {
            // Force the cell to match header width
            (cell as HTMLElement).style.width = `${headerWidth}px`;
            // Force reflow
            void (cell as HTMLElement).offsetHeight;
          });
        });

        // Step 4: Remove force-recalculate class after a delay
        setTimeout(() => {
          grid.classList.remove('force-recalculate');

          // Step 5: Final reflow
          cells.forEach(cell => {
            void (cell as HTMLElement).offsetHeight;
          });
        }, 100);
      });

      // Special handling for charging override grid
      if (chargingOverrideGrid) {
        // Force table layout to be fixed
        const table = chargingOverrideGrid.querySelector('.k-grid-table');
        if (table) {
          (table as HTMLElement).style.tableLayout = 'fixed';
          (table as HTMLElement).style.width = '100%';
        }

        // Force all edit rows to have proper alignment
        const editRows = chargingOverrideGrid.querySelectorAll('.k-grid-edit-row');
        editRows.forEach(row => {
          (row as HTMLElement).style.display = 'table-row';

          // Force all cells in edit rows to have proper alignment
          const editCells = row.querySelectorAll('td');
          editCells.forEach((cell, index) => {
            // Get the width from the corresponding header
            const header = chargingOverrideGrid.querySelector(`.k-grid-header th:nth-child(${index + 1})`);
            if (header) {
              const headerWidth = header.getBoundingClientRect().width;
              (cell as HTMLElement).style.width = `${headerWidth}px`;
              (cell as HTMLElement).style.display = 'table-cell';
              void (cell as HTMLElement).offsetHeight;
            }
          });
        });
      }
    };

    // Execute immediately
    recalculateGridLayout();

    // And schedule another recalculation after a short delay to ensure it works
    gridRefreshTimeout.current = setTimeout(recalculateGridLayout, 200);
  };

  const addNewANumberWhiteList = (): void => {
    const newItem: NumberAnalysisANumberWhiteList = {
      id: generateUniqueId(),
      allowList: null,
      chargingMethod: null,
      prepaidList: null,
      chargingOverrideEnabled: false,
      inEdit: true,
    };

    // Update state with new item
    setNumberAnalysis(prevState => {
      const updatedState = {
        ...prevState,
        anumberWhiteLists: [
          newItem,
          ...(prevState.anumberWhiteLists || []),
        ],
      };

      // Schedule multiple layout recalculations to ensure it works
      // First one immediately after state update
      setTimeout(() => forceGridLayoutRecalculation(), 50);
      // Second one after a short delay
      setTimeout(() => forceGridLayoutRecalculation(), 150);
      // Third one after a longer delay to catch any late DOM updates
      setTimeout(() => forceGridLayoutRecalculation(), 300);

      return updatedState;
    });
  };

  const addNewChargingOverride = (): void => {
    const newItem: NumberAnalysisChargingOverride = {
      id: generateUniqueId(),
      bnumberPrefix: "",
      chargingMethod: null,
      prepaidList: null,
      inEdit: true,
    };

    // Update state with new item
    setNumberAnalysis(prevState => {
      const updatedState = {
        ...prevState,
        chargingOverrideList: [
          newItem,
          ...(prevState.chargingOverrideList || []),
        ],
      };

      // More aggressive approach to layout recalculation
      // First, immediately force a layout recalculation
      requestAnimationFrame(() => {
        forceGridLayoutRecalculation();

        // Then schedule multiple additional recalculations at different intervals
        // to catch any delayed DOM updates
        setTimeout(() => forceGridLayoutRecalculation(), 50);
        setTimeout(() => forceGridLayoutRecalculation(), 150);
        setTimeout(() => forceGridLayoutRecalculation(), 300);
        setTimeout(() => forceGridLayoutRecalculation(), 500);

        // Focus on the first input field of the new row
        setTimeout(() => {
          const firstInput = document.querySelector('.charging-override-grid .k-grid-edit-row td:first-child input');
          if (firstInput) {
            (firstInput as HTMLElement).focus();
          }
        }, 100);
      });

      return updatedState;
    });
  };

  const onANumberWhiteListChange = (event: GridItemChangeEvent): void => {
    const { dataItem, field, value } = event;
    if (!dataItem || !field) return;

    let updatedItem = { ...dataItem, [field]: value };

    // If the charging method is being changed, automatically set the prepaid list
    if (field === 'chargingMethod') {
      const prepaidListId = findPrepaidListForChargingMethod(value);
      if (prepaidListId) {
        updatedItem = { ...updatedItem, prepaidList: prepaidListId };
        console.log(`Setting prepaid list to ${prepaidListId} for charging method ${value}`);
      }
    }

    // If charging override is being enabled and there are no charging override entries, add one automatically
    if (field === 'chargingOverrideEnabled' && value === true &&
        (!numberAnalysis.chargingOverrideList || numberAnalysis.chargingOverrideList.length === 0)) {
      // Add a new charging override entry
      const newChargingOverride: NumberAnalysisChargingOverride = {
        id: generateUniqueId(),
        bnumberPrefix: "",
        chargingMethod: updatedItem.chargingMethod, // Use the same charging method
        prepaidList: updatedItem.prepaidList, // Use the same prepaid list
        inEdit: true,
      };

      // Show a toast notification to inform the user
      toast.info("A Charging Override entry has been added automatically. Please configure it with a B-Number Prefix.");

      // Update the state with the new charging override entry
      setNumberAnalysis(prevState => ({
        ...prevState,
        chargingOverrideList: [newChargingOverride, ...(prevState.chargingOverrideList || [])],
      }));
    }

    const updatedItems = (numberAnalysis.anumberWhiteLists || []).map((item) => {
      if (item && item.id === dataItem.id) {
        return updatedItem;
      }
      return item;
    });

    setNumberAnalysis({
      ...numberAnalysis,
      anumberWhiteLists: updatedItems,
    });
  };

  const onChargingOverrideChange = (event: GridItemChangeEvent): void => {
    const { dataItem, field, value } = event;
    if (!dataItem || !field) return;

    let updatedItem = { ...dataItem, [field]: value };

    // If the charging method is being changed, automatically set the prepaid list
    if (field === 'chargingMethod') {
      const prepaidListId = findPrepaidListForChargingMethod(value);
      if (prepaidListId) {
        updatedItem = { ...updatedItem, prepaidList: prepaidListId };
        console.log(`Setting prepaid list to ${prepaidListId} for charging method ${value} in charging override`);
      }
    }

    const updatedItems = (numberAnalysis.chargingOverrideList || []).map((item) => {
      if (item && item.id === dataItem.id) {
        return updatedItem;
      }
      return item;
    });

    setNumberAnalysis({
      ...numberAnalysis,
      chargingOverrideList: updatedItems,
    });
  };

  const getErrorMessage = (
    gridType: string,
    dataItemId: string | number | undefined,
    field: string
  ): string | undefined => {
    if (!dataItemId) return undefined;

    if (gridType === "anumberWhiteLists") {
      return errors?.anumberWhiteLists?.[dataItemId]?.[field];
    } else if (gridType === "chargingOverrideList") {
      return errors?.chargingOverrideList?.[dataItemId]?.[field];
    }

    return undefined;
  };

  /**
   * Finds the prepaid list ID associated with a charging method
   * @param chargingMethodId The ID of the charging method or the charging method object
   * @returns The ID of the associated prepaid list, or null if not found
   */
  const findPrepaidListForChargingMethod = (chargingMethodId: string | number | object | null): string | number | null => {
    if (!chargingMethodId) return null;

    // Extract the ID from the charging method
    let methodId: string | number;

    if (typeof chargingMethodId === 'object') {
      // Extract ID from object
      const id = (chargingMethodId as { id?: string | number; value?: string | number }).id ||
                 (chargingMethodId as { id?: string | number; value?: string | number }).value;
      if (id === undefined || id === null) return null;
      methodId = id;
      console.log(`Extracted charging method ID ${methodId} from object:`, chargingMethodId);
    } else {
      // Already a primitive value
      methodId = chargingMethodId;
      console.log(`Using primitive charging method ID: ${methodId}`);
    }

    // Find the charging method in the list
    const chargingMethod = chargingMethods.find(method => {
      // Compare as strings to handle type mismatches
      return String(method.id) === String(methodId);
    });

    if (!chargingMethod) {
      console.log(`Charging method with ID ${methodId} not found in list of ${chargingMethods.length} methods`);
      // Log all available charging methods for debugging
      if (chargingMethods.length > 0) {
        console.log("Available charging methods:", chargingMethods.map(m => `${m.id}: ${m.name}`).join(", "));
      }
      return null;
    }

    console.log(`Found charging method: ${chargingMethod.name} with ID ${chargingMethod.id}`);

    if (chargingMethod.anumberPrepaidList) {
      // If the charging method has an associated prepaid list, return its ID
      if (typeof chargingMethod.anumberPrepaidList === 'object' && chargingMethod.anumberPrepaidList !== null) {
        const prepaidListId = (chargingMethod.anumberPrepaidList as NumberList).id || null;
        console.log(`Found prepaid list ID (object): ${prepaidListId}`);
        return prepaidListId;
      }
      // If it's already a string or number, return it directly
      if (typeof chargingMethod.anumberPrepaidList === 'string' || typeof chargingMethod.anumberPrepaidList === 'number') {
        console.log(`Found prepaid list ID (primitive): ${chargingMethod.anumberPrepaidList}`);
        return chargingMethod.anumberPrepaidList;
      }
    }

    // If no prepaid list is found, return the first available prepaid list as a fallback
    if (numberLists.length > 0) {
      const firstPrepaidList = numberLists[0];
      console.log(`No prepaid list found for charging method ${methodId}, using first available list: ${firstPrepaidList.id}`);
      return firstPrepaidList.id;
    }

    // If no prepaid list is found and no fallback is available, return null
    console.log(`No prepaid list found for charging method ${methodId} and no fallback available`);
    return null;
  };

  const goBack = (): void => {
    navigate("/");
  };

  const numberListCell = (props: { dataItem: any; field?: string }, gridType: string, field: string): JSX.Element | null => {
    if (!props.dataItem) {
      return null;
    }

    // If this is a prepaid list field, make it read-only and display the value based on charging method
    if (field === "prepaidList") {
      // Find the prepaid list associated with the charging method
      const chargingMethodId = props.dataItem.chargingMethod;
      let prepaidListName = "";

      // If there's a charging method selected, find the associated prepaid list
      if (chargingMethodId) {
        // Get the prepaid list ID from the charging method
        const prepaidListId = findPrepaidListForChargingMethod(chargingMethodId);

        if (prepaidListId) {
          // Find the prepaid list name from the numberLists array
          const prepaidList = numberLists.find(list => {
            // Compare as strings to handle type mismatches
            return String(list.id) === String(prepaidListId);
          });

          if (prepaidList) {
            prepaidListName = prepaidList.name || "";
          } else {
            // Fallback if the list is not found
            prepaidListName = "Prepaid List " + prepaidListId;
          }
        } else {
          // Fallback to a default name if no prepaid list is associated
          prepaidListName = "Default Subscribers";
        }
      }

      return (
        <td className="k-grid-content-cell">
          <div style={{ padding: '4px 8px' }}>{prepaidListName}</div>
        </td>
      );
    }

    // For other fields, use the SelectCell as before
    const options = numberLists.map(list => ({
      id: list.id,
      value: list.id,
      name: list.name || "",
      displayText: list.name || ""
    }));

    // Ensure dataItem is properly passed to SelectCell
    return (
      <SelectCell
        dataItem={props.dataItem}
        field={props.field}
        options={options}
        onChange={(e: { value?: any; target?: { value?: any } } | any) => {
          // Get the actual value from the event
          // The SelectCell component returns either the selected option or the raw value
          const value = typeof e === 'object' && e.value !== undefined ? e.value :
                        e.target?.value !== undefined ? e.target.value : e;

          console.log(`numberListCell onChange: field=${field}, value=`, value);

          // Convert to a GridItemChangeEvent format
          const gridEvent: GridItemChangeEvent = {
            dataItem: props.dataItem,
            field: field,
            value: value,
            syntheticEvent: e
          };

          if (gridType === "anumberWhiteLists") {
            onANumberWhiteListChange(gridEvent);
          } else {
            onChargingOverrideChange(gridEvent);
          }
        }}
        error={getErrorMessage(gridType, props.dataItem.id, field)}
        style={{ width: '100%' }}
      />
    );
  };

  const chargingMethodCell = (props: { dataItem: any; field?: string }, gridType: string): JSX.Element | null => {
    if (!props.dataItem) {
      return null;
    }

    // Convert chargingMethods to the format expected by SelectCell
    const options = chargingMethods.map(method => ({
      id: method.id,
      value: method.id,
      name: method.name || "",
      displayText: method.name || ""
    }));

    // Ensure dataItem is properly passed to SelectCell
    return (
      <SelectCell
        dataItem={props.dataItem}
        field={props.field || "chargingMethod"}
        options={options}
        onChange={(e: { value?: any; target?: { value?: any } } | any) => {
          // Get the actual value from the event
          // The SelectCell component returns either the selected option or the raw value
          const value = typeof e === 'object' && e.value !== undefined ? e.value :
                        e.target?.value !== undefined ? e.target.value : e;

          console.log(`chargingMethodCell onChange: value=`, value);

          // Convert to a GridItemChangeEvent format
          const gridEvent: GridItemChangeEvent = {
            dataItem: props.dataItem,
            field: "chargingMethod",
            value: value,
            syntheticEvent: e
          };

          if (gridType === "anumberWhiteLists") {
            onANumberWhiteListChange(gridEvent);
          } else {
            onChargingOverrideChange(gridEvent);
          }
        }}
        error={getErrorMessage(gridType, props.dataItem.id, "chargingMethod")}
        style={{ width: '100%' }}
      />
    );
  };

  return (
    <div style={{ padding: "0.5em" }}>
      <div style={{
        padding: "1em 1.5em",
        color: "#666",
        lineHeight: "1.5",
        margin: "0 0 1em 0"
      }}>
        The SMSC checks the incoming A- and B-numbers against Allow and Block lists of number prefixes. If a number isn't on the Allow list or appears on the Block list, the message delivery is cancelled. After screening, the Charging Method is set based on the A-Number Allow list mapping.
      </div>
      <ValidatorForm
        ref={formRef}
        onSubmit={handleSubmit}
        onError={(errors) => console.log("ValidatorForm errors:", errors)}
        className="tango-form"
      >
        <div style={{ marginLeft: "1em" }}>
          {isLoading ? (
            <Card>
              <CardContent style={{ display: 'flex', justifyContent: 'center', padding: '2em' }}>
                <div>Loading data...</div>
              </CardContent>
            </Card>
          ) : (
            <>
              <ErrorsDisplay
                errorResponse={errorResponse}
                keyPrefix="smsws.numberAnalysis.form.keys"
              />

              {/* Reload Charging Methods Card */}
              <Card style={{ marginBottom: "1.5em" }}>
                <CardContent>
                  <div className="section-header">
                    <h3>Reload Charging Methods</h3>
                    <p>Charging Methods are loaded from GSM_/Smsc.cfg or startup. Prepaid Prefix Lists are auto-generated from the same file and should be populated via the "Number Lists" screen. To apply changes after modifying GSM_/Smsc.cfg, click the button below to reload Charging Methods. Specify a host name if loading from a particular host is required.</p>
                  </div>
                  <div className="reload-charging-methods">
                    <TextValidator
                      label="Host"
                      onChange={handleChange}
                      name="chargingMethodReloadHost"
                      value={numberAnalysis.chargingMethodReloadHost || ""}
                      validators={["required"]}
                      errorMessages={["Host is required"]}
                      size="small"
                    />
                    <span className="optional-label"></span>
                    <Button
                      variant="contained"
                      color="primary"
                      size="small"
                      onClick={() => {
                        if (!numberAnalysis.chargingMethodReloadHost) {
                          toast.error("Host is required");
                          return;
                        }

                        setIsReloading(true);
                        SmscNumberAnalysisService.reloadChargingMethods(numberAnalysis.chargingMethodReloadHost)
                          .then(() => {
                            toast.success(
                              i18n["smsws.numberAnalysis.form.reloadSuccess"] ||
                                "Charging methods reloaded successfully"
                            );

                            // Reload charging methods directly from the service
                            return SmscNumberAnalysisService.getChargingMethods();
                          })
                          .then((chargingMethodsData) => {
                            if (chargingMethodsData) {
                              setChargingMethods(chargingMethodsData);
                            }
                          })
                          .catch((error) => {
                            toast.error(
                              (i18n["smsws.numberAnalysis.form.reloadError"] ||
                                "Failed to reload charging methods: ") + error.message
                            );
                          })
                          .finally(() => {
                            setIsReloading(false);
                          });
                      }}

                      disabled={isReloading}
                    >
                      {isReloading
                        ? (i18n["button.reloading"] || "Reloading...")
                        : (i18n["button.reload"] || "Reload")}
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Number Analysis Configuration Card */}
              <Card>
                <CardContent>

              <Accordion
                defaultExpanded
                className="boxed-accordion">
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Typography variant="h6" className="accordion-title">
                    {i18n["smsc.numberAnalysis.accordion.aNumberAllowLists"] || "A-Number Allow Lists"}
                  </Typography>
                </AccordionSummary>
                <AccordionDetails>
                  <div className="description-text">
                    <p>Here you configure Allow Lists and associate them with Charging Methods.</p>
                  </div>
                  {errors?.anumberWhiteLists?.general?.chargingOverride && (
                    <div className="error-message" style={{ color: 'red', marginBottom: '1em' }}>
                      {errors.anumberWhiteLists.general.chargingOverride}
                    </div>
                  )}
                  <Grid
                    data={numberAnalysis.anumberWhiteLists || []}
                    editField="inEdit"
                    onItemChange={onANumberWhiteListChange}
                    style={{ marginTop: "1em" }}
                  >
                    <GridToolbar>
                      <button
                        type="button"
                        className="k-button"
                        style={{ position: "absolute", right: "1em" }}
                        onClick={addNewANumberWhiteList}
                      >
                        {i18n["button.add"] || "Add"}
                      </button>
                    </GridToolbar>
                    <Column
                      field="allowList"
                      title="Allow List"
                      cell={(props) => numberListCell(props, "anumberWhiteLists", "allowList")}
                    />
                    <Column
                      field="chargingMethod"
                      title="Charging Method"
                      cell={(props) => chargingMethodCell(props, "anumberWhiteLists")}
                    />
                    <Column
                      field="prepaidList"
                      title="Prepaid List"
                      cell={(props) => numberListCell(props, "anumberWhiteLists", "prepaidList")}
                    />
                    <Column
                      field="chargingOverrideEnabled"
                      title="Charging Override Enabled"
                      cell={(props) => (
                        <BooleanYesNoCell
                          {...props}
                          onChange={onANumberWhiteListChange}
                        />
                      )}
                    />
                    <Column
                      cell={(props) => (
                        <CommandCell
                          {...props}
                          gridProp="anumberWhiteLists"
                          item={numberAnalysis}
                          onChange={(data) =>
                            setNumberAnalysis({
                              ...numberAnalysis,
                              anumberWhiteLists: data as NumberAnalysisANumberWhiteList[],
                            })
                          }
                          onSave={(dataItem) => {
                            // Validate the item
                            const itemErrors: Record<string, string> = {};
                            if (!dataItem.allowList) {
                              itemErrors.allowList = "Allow List is required";
                            }
                            if (!dataItem.chargingMethod) {
                              itemErrors.chargingMethod = "Charging Method is required";
                            }

                            if (Object.keys(itemErrors).length > 0) {
                              setErrors((prev) => ({
                                ...prev,
                                anumberWhiteLists: {
                                  ...prev?.anumberWhiteLists,
                                  [dataItem.id]: itemErrors,
                                },
                              }));
                              Object.values(itemErrors).forEach((error) => {
                                toast.error(error);
                              });
                              return false;
                            }

                            // Schedule multiple layout recalculations after save
                            // to ensure the grid layout is properly updated
                            setTimeout(() => forceGridLayoutRecalculation(), 50);
                            setTimeout(() => forceGridLayoutRecalculation(), 150);
                            setTimeout(() => forceGridLayoutRecalculation(), 300);
                            return true;
                          }}
                          onCancel={() => {
                            // Schedule multiple layout recalculations after cancel
                            // to ensure the grid layout is properly updated
                            setTimeout(() => forceGridLayoutRecalculation(), 50);
                            setTimeout(() => forceGridLayoutRecalculation(), 150);
                            setTimeout(() => forceGridLayoutRecalculation(), 300);
                          }}
                        />
                      )}
                      filterable={false}
                    />
                  </Grid>
                </AccordionDetails>
              </Accordion>

              <Accordion
                defaultExpanded
                className="boxed-accordion">
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Typography variant="h6" className="accordion-title">
                    {i18n["smsc.numberAnalysis.accordion.aNumberBlackList"] || "A-Number Block List"}
                  </Typography>
                </AccordionSummary>
                <AccordionDetails>
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <SelectValidator
                      label="A-Number Block List"
                      onChange={handleSelectChange}
                      name="anumberBlackList"
                      value={numberAnalysis.anumberBlackList || ""}
                      select
                      size="small"
                      SelectProps={{
                        MenuProps: {
                          PaperProps: {
                            style: {
                              maxHeight: 300
                            }
                          }
                        }
                      }}
                    >
                      <MenuItem value="">
                        <em>None</em>
                      </MenuItem>
                      {numberLists.map((list) => (
                        <MenuItem key={list.id} value={list.id}>
                          {list.name}
                        </MenuItem>
                      ))}
                    </SelectValidator>
                    <span className="optional-label"></span>
                  </div>
                </AccordionDetails>
              </Accordion>

              <Accordion
                defaultExpanded
                className="boxed-accordion">
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Typography variant="h6" className="accordion-title">
                    {i18n["smsc.numberAnalysis.accordion.chargingOverride"] || "Charging Override"}
                  </Typography>
                </AccordionSummary>
                <AccordionDetails>
                  <div className="section-description">
                    <p>Charging Override occurs when A-Number is on an Allow list with Charging Override enabled and B-Number is configured in the table below. It only occurs on the P2P/P2A/First Delivery Attempt and A2P flows.</p>
                  </div>
                  <Grid
                    editField="inEdit"
                    editable={true}
                    onItemChange={onChargingOverrideChange}
                    style={{
                      width: "100%",
                      marginTop: "1em"
                    }}
                    data={numberAnalysis.chargingOverrideList || []}
                    total={numberAnalysis.chargingOverrideList?.length || 0}
                    reorderable={false}
                    resizable={true}
                    className="charging-override-grid"
                  >
                    <GridToolbar>
                      <button
                        type="button"
                        className="k-primary k-button k-grid-edit-command"
                        style={{ position: "absolute", right: "1em" }}
                        onClick={addNewChargingOverride}
                      >
                        {i18n["button.add"] || "Add"}
                      </button>
                    </GridToolbar>
                    <Column
                      field="bnumberPrefix"
                      title="Charging Override B Number Prefix"
                      cell={(props) => (
                        <InputCell
                          {...props}
                          onChange={onChargingOverrideChange}
                          error={getErrorMessage(
                            "chargingOverrideList",
                            props.dataItem?.id,
                            "bnumberPrefix"
                          )}
                          debounceDelay={500} // Add debounce to improve focus retention
                        />
                      )}
                    />
                    <Column
                      field="chargingMethod"
                      title="Charging Method"
                      cell={(props) => chargingMethodCell(props, "chargingOverrideList")}
                    />
                    <Column
                      field="prepaidList"
                      title="Prepaid List"
                      cell={(props) => numberListCell(props, "chargingOverrideList", "prepaidList")}
                    />
                    <Column
                      cell={(props) => (
                        <CommandCell
                          {...props}
                          gridProp="chargingOverrideList"
                          item={numberAnalysis}
                          onChange={(data) =>
                            setNumberAnalysis({
                              ...numberAnalysis,
                              chargingOverrideList: data as NumberAnalysisChargingOverride[],
                            })
                          }
                          onSave={(dataItem) => {
                            // Validate the item
                            const itemErrors: Record<string, string> = {};
                            if (!dataItem.bnumberPrefix) {
                              itemErrors.bnumberPrefix = "B-Number Prefix is required";
                            }
                            if (!dataItem.chargingMethod) {
                              itemErrors.chargingMethod = "Charging Method is required";
                            }

                            if (Object.keys(itemErrors).length > 0) {
                              setErrors((prev) => ({
                                ...prev,
                                chargingOverrideList: {
                                  ...prev?.chargingOverrideList,
                                  [dataItem.id]: itemErrors,
                                },
                              }));
                              Object.values(itemErrors).forEach((error) => {
                                toast.error(error);
                              });
                              return false;
                            }

                            // Use requestAnimationFrame for smoother layout recalculation
                            requestAnimationFrame(() => {
                              // Immediate recalculation
                              forceGridLayoutRecalculation();

                              // Schedule multiple additional recalculations at different intervals
                              // to catch any delayed DOM updates
                              setTimeout(() => forceGridLayoutRecalculation(), 50);
                              setTimeout(() => forceGridLayoutRecalculation(), 150);
                              setTimeout(() => forceGridLayoutRecalculation(), 300);
                              setTimeout(() => forceGridLayoutRecalculation(), 500);
                            });

                            return true;
                          }}
                          onCancel={() => {
                            // Use requestAnimationFrame for smoother layout recalculation
                            requestAnimationFrame(() => {
                              // Immediate recalculation
                              forceGridLayoutRecalculation();

                              // Schedule multiple additional recalculations at different intervals
                              // to catch any delayed DOM updates
                              setTimeout(() => forceGridLayoutRecalculation(), 50);
                              setTimeout(() => forceGridLayoutRecalculation(), 150);
                              setTimeout(() => forceGridLayoutRecalculation(), 300);
                              setTimeout(() => forceGridLayoutRecalculation(), 500);
                            });
                          }}
                        />
                      )}
                      filterable={false}
                    />
                     </Grid>
                </AccordionDetails>
              </Accordion>

              <Accordion
                defaultExpanded
                className="boxed-accordion">
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Typography variant="h6" className="accordion-title">
                    {i18n["smsc.numberAnalysis.accordion.bNumber"] || "B-Number"}
                  </Typography>
                </AccordionSummary>
                <AccordionDetails>
                  <div className="b-number-selects">
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      <SelectValidator
                        label="B-Number Allow List"
                        onChange={handleSelectChange}
                        name="bnumberWhiteList"
                        value={numberAnalysis.bnumberWhiteList || ""}
                        select
                        size="small"
                        SelectProps={{
                          MenuProps: {
                            PaperProps: {
                              style: {
                                maxHeight: 300
                              }
                            }
                          }
                        }}
                      >
                        <MenuItem value="">
                          <em>None</em>
                        </MenuItem>
                        {numberLists.map((list) => (
                          <MenuItem key={list.id} value={list.id}>
                            {list.name}
                          </MenuItem>
                        ))}
                      </SelectValidator>
                      <span className="optional-label"></span>
                    </div>
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      <SelectValidator
                        label="B-Number Block List"
                        onChange={handleSelectChange}
                        name="bnumberBlackList"
                        value={numberAnalysis.bnumberBlackList || ""}
                        select
                        size="small"
                        SelectProps={{
                          MenuProps: {
                            PaperProps: {
                              style: {
                                maxHeight: 300
                              }
                            }
                          }
                        }}
                      >
                        <MenuItem value="">
                          <em>None</em>
                        </MenuItem>
                        {numberLists.map((list) => (
                          <MenuItem key={list.id} value={list.id}>
                            {list.name}
                          </MenuItem>
                        ))}
                      </SelectValidator>
                      <span className="optional-label"></span>
                    </div>
                  </div>
                </AccordionDetails>
              </Accordion>
                </CardContent>
                <CardActions className="card-actions content-card-actions">
                  <Button
                    variant="contained"
                    color="secondary"
                    onClick={goBack}
                    disabled={isSubmitting}
                  >
                    {i18n["button.cancel"] || "Cancel"}
                  </Button>
                  <Button
                    variant="contained"
                    color="primary"
                    type="button"
                    onClick={handleSubmit}
                    disabled={isSubmitting}
                  >
                    {isSubmitting
                      ? (i18n["button.saving"] || "Saving...")
                      : (i18n["button.submit"] || "Submit")}
                  </Button>
                </CardActions>
              </Card>
            </>
          )}
        </div>
      </ValidatorForm>
    </div>
  );
};

export default SmscNumberAnalysisForm;
