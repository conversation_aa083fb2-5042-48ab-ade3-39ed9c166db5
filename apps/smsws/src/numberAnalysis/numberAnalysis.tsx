import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON>, CardContent, CardHeader } from "@mui/material";
import { MaterialTable, tableIcons } from "@pnmui/common";
import { $i18n } from "@pnmui/common/services/propertiesService";
import { SecurityService } from "@pnmui/common/services/securityService";
import { SmscNumberAnalysisService } from "@pnmui/common/services/smsc/numberAnalysisService";
import { paginate } from "@pnmui/common/utils";
import { useNavigate } from "react-router-dom";
import { toast } from "react-toastify";
import { Subscription } from "rxjs";
import { tap } from "rxjs/operators";
import { NumberAnalysis } from "../types";

interface TableData {
  data: NumberAnalysis[];
  page: number;
  totalCount: number;
}

function SmscNumberAnalysis() {
  const [numberAnalysisList, setNumberAnalysisList] = useState<NumberAnalysis[]>([]);
  const [i18n, setI18n] = useState<Record<string, string>>({});
  const [selectedRows, setSelectedRows] = useState<(string | number)[]>([]);
  const navigate = useNavigate();
  const tableRef = React.createRef<any>();

  useEffect(() => {
    const subscription: Subscription = $i18n
      .pipe(tap())
      .subscribe((i18NProps) => {
        if (i18NProps) {
          setI18n(i18NProps);
        }
      });
    loadNumberAnalysis();

    return () => subscription.unsubscribe();
  }, []);

  const loadNumberAnalysis = () => {
    SmscNumberAnalysisService.getNumberAnalysis()
      .then((data) => {
        setNumberAnalysisList(data);
      })
      .catch((error) => {
        toast.error("Failed to load number analysis: " + error.message);
      });
  };

  const handleEdit = (id: string | number) => {
    navigate(`/smscNumberAnalysisForm/${id}`);
  };

  const handleAdd = () => {
    navigate("/smscNumberAnalysisForm/new");
  };

  const handleDelete = (ids: (string | number)[]) => {
    if (ids.length === 0) {
      toast.warning("No items selected for deletion");
      return;
    }

    if (window.confirm("Are you sure you want to delete the selected items?")) {
      const deletePromises = ids.map((id) =>
        SmscNumberAnalysisService.deleteNumberAnalysisById(id)
      );

      Promise.all(deletePromises)
        .then(() => {
          toast.success("Selected items deleted successfully");
          loadNumberAnalysis();
          setSelectedRows([]);
        })
        .catch((error) => {
          toast.error("Failed to delete items: " + error.message);
        });
    }
  };

  return (
    <div style={{ padding: "0.5em" }}>
      <Card>
        <CardHeader title={"MO (P2A, P2P) & AO (A2P) Number Analysis"} />
        <CardContent>
          <MaterialTable
            tableRef={tableRef}
            columns={[
              {
                title: "ID",
                field: "id",
                hidden: true,
              },
              {
                title: "Charging Method Reload Host",
                field: "chargingMethodReloadHost",
              },
              {
                title: "Created Time",
                field: "createdTime",
                type: "datetime",
              },
              {
                title: "Last Updated Time",
                field: "lastUpdatedTime",
                type: "datetime",
              },
            ]}
            data={(query) => {
              return new Promise<TableData>((resolve) => {
                const { page, pageSize } = query;
                const paginatedData = paginate(numberAnalysisList, pageSize, page);
                resolve({
                  data: paginatedData,
                  page,
                  totalCount: numberAnalysisList.length,
                });
              });
            }}
            title=""
            icons={tableIcons}
            options={{
              selection: true,
              actionsColumnIndex: -1,
              pageSize: 10,
              pageSizeOptions: [10, 20, 50],
              headerStyle: {
                backgroundColor: "#f5f5f5",
                color: "#000",
              },
              rowStyle: (rowData) => ({
                backgroundColor:
                  selectedRows.indexOf(rowData.id) !== -1 ? "#EEE" : "#FFF",
              }),
            }}
            onSelectionChange={(rows) => {
              setSelectedRows(rows.map((row) => row.id));
            }}
            actions={[
              {
                icon: tableIcons.Add,
                tooltip: "Add",
                isFreeAction: true,
                onClick: () => handleAdd(),
              },
              {
                icon: tableIcons.Edit,
                tooltip: "Edit",
                onClick: (event, rowData) => {
                  if (Array.isArray(rowData)) {
                    // Handle multiple rows
                    if (rowData.length === 1) {
                      handleEdit(rowData[0].id);
                    } else {
                      toast.warning("Please select only one row to edit");
                    }
                  } else {
                    // Handle single row
                    handleEdit(rowData.id);
                  }
                },
              },
              {
                icon: tableIcons.Delete,
                tooltip: "Delete",
                onClick: () => handleDelete(selectedRows),
              },
            ]}
            onRowClick={(event, rowData) => {
              if (rowData) {
                handleEdit(rowData.id);
              }
            }}
          />
        </CardContent>
      </Card>
    </div>
  );
}

export default SmscNumberAnalysis;
