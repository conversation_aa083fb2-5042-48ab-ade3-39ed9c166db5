import React from 'react';
import { useLocation } from 'react-router-dom';
import './Header.css';

const Header: React.FC = () => {
  const location = useLocation();

  // Function to get the page title based on the current route
  const getPageTitle = () => {
    const path = location.pathname;

    // List pages
    if (path === '/' || path === '/smscApplications') {
      return 'SMSC Applications';
    }

    // Map routes to titles for list pages
    const listPageTitles: Record<string, string> = {
      '/smscConnections': 'SMSC Connections',
      '/smscDeliveryAndRetryProfiles': 'Delivery & Retry Profiles',
      '/smscDeliveryAndRetrySchedules': 'Delivery & Retry Schedules',
      '/smscIpLists': 'IP Lists',
      '/smscNumberLists': 'SMSC Number Lists',
      '/smscResourcePolicies': 'Resource Policies',
      '/smscRoutingClasses': 'Routing Classes',
      '/smscSmppApplicationProfiles': 'SMPP Application Profiles',
      '/smscSmsRoutingTables': 'SMS Routing Tables',
      '/smscSpamWords': 'Spam Words',
      '/smscNumberAnalysis': 'MO (P2A, P2P) & AO (A2P) Number Analysis',
    };

    // Map routes to titles for form pages
    const formPageTitles: Record<string, string> = {
      '/smscApplicationForm': 'SMSC Application',
      '/smscConnectionForm': 'SMSC Connection',
      '/smscDeliveryAndRetryProfileForm': 'Delivery & Retry Profile',
      '/smscDeliveryAndRetryScheduleForm': 'Delivery & Retry Schedule',
      '/smscIpListsForm': 'IP List',
      '/smscNumberListsForm': 'Number List',
      '/smscResourcePolicyForm': 'Resource Policy',
      '/smscRoutingClassForm': 'Routing Class',
      '/smscSmppApplicationProfileForm': 'SMPP Application Profile',
      '/smscSmsRoutingTableForm': 'SMS Routing Table',
      '/smscSpamWordForm': 'Spam Word',
    };

    // Check if the path matches any form page
    for (const route in formPageTitles) {
      if (path.includes(route)) {
        return formPageTitles[route];
      }
    }

    // Check if the path matches any list page
    for (const route in listPageTitles) {
      if (path === route) {
        return listPageTitles[route];
      }
    }

    // Special case for strip and replace
    if (path.includes('/smscStripAndReplace/a-number')) {
      return 'Strip And Replace A-Number';
    } else if (path.includes('/smscStripAndReplace/b-number')) {
      return 'Strip And Replace B-Number';
    }

    return 'SMSC Applications';
  };

  return (
    <header className="app-header">
      <h1 className="page-title">{getPageTitle()}</h1>
      {/* <div className="header-actions">
        <button className="header-action-btn">Change Password</button>
        <button className="header-action-btn">Help</button>
        <button className="header-action-btn">Log Out</button>
        <button className="header-action-btn">Language</button>
      </div> */}
    </header>
  );
};

export default Header;
