.app-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  height: 60px;
  background-color: #fff;
  border-bottom: 1px solid #e0e0e0;
}

.page-title {
  font-size: 24px;
  color: #333;
  margin: 0;
  font-weight: normal;
}

.header-actions {
  display: flex;
  gap: 15px;
}

.header-action-btn {
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  padding: 5px 10px;
}

.header-action-btn:hover {
  text-decoration: underline;
}

.header-action-btn svg {
  margin-right: 5px;
}
