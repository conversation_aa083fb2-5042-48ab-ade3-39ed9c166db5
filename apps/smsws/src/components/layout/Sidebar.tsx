import React, { useState } from 'react';
import { NavLink, useNavigate } from 'react-router-dom';
import './Sidebar.css';
import CSGLogo from './CSGLogo';

// Import icons from Material UI
import SmsIcon from '@mui/icons-material/Sms';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';

interface MenuItemProps {
  icon: React.ReactNode;
  label: string;
  to?: string;
  children?: React.ReactNode;
  defaultExpanded?: boolean;
}

const MenuItem: React.FC<MenuItemProps> = ({
  icon,
  label,
  to,
  children,
  defaultExpanded = false
}) => {
  // Always expanded for SMSC menu
  const [expanded, setExpanded] = useState(true);

  const toggleExpand = (e: React.MouseEvent) => {
    if (!to) {
      e.preventDefault();
      setExpanded(!expanded);
    }
  };

  return (
    <div className={`menu-item ${expanded ? 'expanded' : ''}`}>
      {to ? (
        <NavLink
          to={to}
          className={({ isActive }) => isActive ? 'menu-link active' : 'menu-link'}
          onClick={toggleExpand}
        >
          <span className="menu-icon">{icon}</span>
          <span className="menu-label">{label}</span>
          {children && (
            <span className="menu-expand">
              {expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
            </span>
          )}
        </NavLink>
      ) : (
        <div className="menu-link" onClick={toggleExpand}>
          <span className="menu-icon">{icon}</span>
          <span className="menu-label">{label}</span>
          {children && (
            <span className="menu-expand">
              {expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
            </span>
          )}
        </div>
      )}

      {children && expanded && (
        <div className="submenu">{children}</div>
      )}
    </div>
  );
};

interface NestedSubMenuItemProps {
  label: string;
  to: string;
}

const NestedSubMenuItem: React.FC<NestedSubMenuItemProps> = ({ label, to }) => {
  const navigate = useNavigate();
  
  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault();
    navigate(to, { replace: true });
  };

  return (
    <div className="nested-submenu-item">
      <NavLink
        to={to}
        className={({ isActive }) => isActive ? 'nested-submenu-link active' : 'nested-submenu-link'}
        onClick={handleClick}
      >
        <span className="nested-submenu-label">{label}</span>
      </NavLink>
    </div>
  );
};

const Sidebar: React.FC = () => {
  return (
    <div className="sidebar">
      <div className="logo-container">
        <CSGLogo />
      </div>

      <nav className="menu">
        <MenuItem icon={<SmsIcon />} label="SMSC" defaultExpanded={true}>
          <NestedSubMenuItem label="Applications" to="smscApplications" />
          <NestedSubMenuItem label="Connections" to="smscConnections" />
          <NestedSubMenuItem label="Application Profiles" to="smscSmppApplicationProfiles" />
          <NestedSubMenuItem label="Delivery and Retry Profiles" to="smscDeliveryAndRetryProfiles" />
          <NestedSubMenuItem label="Delivery and Retry Schedules" to="smscDeliveryAndRetrySchedules" />
          <NestedSubMenuItem label="SMS Routing Classes" to="smscRoutingClasses" />
          <NestedSubMenuItem label="SMS Routing Tables" to="smscSmsRoutingTables" />
          <NestedSubMenuItem label="Resource Policies" to="smscResourcePolicies" />
          <NestedSubMenuItem label="Number Lists" to="smscNumberLists" />
          <NestedSubMenuItem label="Spam Registries" to="smscSpamWords" />
          <NestedSubMenuItem label="IP Lists" to="smscIpLists" />
          <NestedSubMenuItem label="Strip And Replace A-Number" to="smscStripAndReplace/a-number" />
          <NestedSubMenuItem label="Strip And Replace B-Number" to="smscStripAndReplace/b-number" />
          <NestedSubMenuItem label="Number Analysis" to="smscNumberAnalysis" />
        </MenuItem>
      </nav>
    </div>
  );
};

export default Sidebar;
