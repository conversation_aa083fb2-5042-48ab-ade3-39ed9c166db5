import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  FormHelperText,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Typography,
} from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import ErrorsDisplay from "@pnmui/common/components/errorsDisplay";
import { $i18n } from "@pnmui/common/services/propertiesService";
import { SmscEnumerationsService } from "@pnmui/common/services/smsc/enumerationsService";
import React, { useEffect, useState } from "react";
import { toast } from "react-toastify";
import { Subscription } from "rxjs";
import { tap } from "rxjs/operators";
import "../common.css";
import "./stripAndReplaceFormDialog.css";
import {
  EnumerationOption,
  StripAndReplaceEntry,
  StripAndReplaceType,
} from "../types";

interface FormErrors {
  [key: string]: string | FormErrors;
}

interface StripAndReplaceFormDialogProps {
  open: boolean;
  onClose: () => void;
  onSave: (entry: StripAndReplaceEntry) => void;
  entry?: StripAndReplaceEntry;
  type: StripAndReplaceType;
  isEditing: boolean;
}

const StripAndReplaceFormDialog: React.FC<StripAndReplaceFormDialogProps> = ({
  open,
  onClose,
  onSave,
  entry,
  type,
  isEditing,
}) => {
  const [i18n, setI18n] = useState<Record<string, string>>({});
  const [errorResponse, setErrorResponse] = useState<any>(null);
  const [formEntry, setFormEntry] = useState<StripAndReplaceEntry>(
    entry || {
      id: `new-${Date.now()}`,
      stripNumber: "",
      replaceNumber: "",
      minDigits: undefined,
      maxDigits: undefined,
      stripTypeOfNumber: "ANY",
      stripNumberPlanIndicator: "ANY",
      replaceTypeOfNumber: "KEEP",
      replaceNumberPlanIndicator: "KEEP",
    }
  );
  const [typeOfNumberOptions, setTypeOfNumberOptions] = useState<
    EnumerationOption[]
  >([]);
  const [numberPlanIndicatorOptions, setNumberPlanIndicatorOptions] = useState<
    EnumerationOption[]
  >([]);
  const [errors, setErrors] = useState<FormErrors>({});

  useEffect(() => {
    const subscription: Subscription = $i18n
      .pipe(tap())
      .subscribe((i18NProps) => {
        if (i18NProps) {
          setI18n(i18NProps);
        }
      });

    // Load enumerations
    SmscEnumerationsService.getEnumerations()
      .then((response: Record<string, EnumerationOption[]>) => {
        const typeOfNumber = response.typeOfNumber || [];
        const numberPlanIndicator = response.numberPlanIndicator || [];
        setTypeOfNumberOptions(typeOfNumber);
        setNumberPlanIndicatorOptions(numberPlanIndicator);
      })
      .catch((error: any) => {
        toast.error("Failed to fetch enumerations: " + error.message);
      });

    // Reset form when entry changes
    if (entry) {
      setFormEntry(entry);
    } else {
      setFormEntry({
        id: `new-${Date.now()}`,
        stripNumber: "",
        replaceNumber: "",
        minDigits: undefined,
        maxDigits: undefined,
        stripTypeOfNumber: "ANY",
        stripNumberPlanIndicator: "ANY",
        replaceTypeOfNumber: "KEEP",
        replaceNumberPlanIndicator: "KEEP",
      });
    }

    return () => subscription.unsubscribe();
  }, [entry, open]);

  const handleSubmit = () => {
    // Validate form
    const validationErrors = validateForm();
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);

      // Show toast messages for validation errors
      Object.values(validationErrors).forEach(error => {
        if (typeof error === 'string') {
          toast.error(error);
        }
      });

      return;
    }

    // Process the entry before saving
    const processedEntry = {
      ...formEntry,
      // Convert KEEP and ANY values to null for the server
      stripTypeOfNumber: formEntry.stripTypeOfNumber === "ANY" ? null : formEntry.stripTypeOfNumber,
      stripNumberPlanIndicator: formEntry.stripNumberPlanIndicator === "ANY" ? null : formEntry.stripNumberPlanIndicator,
      replaceTypeOfNumber: formEntry.replaceTypeOfNumber === "KEEP" ? null : formEntry.replaceTypeOfNumber,
      replaceNumberPlanIndicator: formEntry.replaceNumberPlanIndicator === "KEEP" ? null : formEntry.replaceNumberPlanIndicator,
    };

    // Call onSave with the processed entry
    onSave(processedEntry);
    onClose();
  };

  const validateForm = (): FormErrors => {
    const errors: FormErrors = {};

    if (!formEntry.stripNumber) {
      errors.stripNumber = "Strip Number is required";
    }

    // Min/Max digits are optional, but if provided they must be valid
    if (formEntry.minDigits !== undefined && formEntry.minDigits < 1) {
      errors.minDigits = "Min Digits must be at least 1";
    }

    if (
      formEntry.maxDigits !== undefined &&
      formEntry.minDigits !== undefined &&
      formEntry.maxDigits < formEntry.minDigits
    ) {
      errors.maxDigits =
        "Max Digits must be greater than or equal to Min Digits";
    }

    return errors;
  };

  const handleChange = (field: string, value: any) => {
    setFormEntry({
      ...formEntry,
      [field]: value,
    });
  };

  const getTitle = () => {
    if (isEditing) {
      return type === StripAndReplaceType.A_NUMBER
        ? i18n["smsc.stripAndReplace.aNumber.editTitle"] ||
            "Edit A-Number Strip and Replace Entry"
        : i18n["smsc.stripAndReplace.bNumber.editTitle"] ||
            "Edit B-Number Strip and Replace Entry";
    } else {
      return type === StripAndReplaceType.A_NUMBER
        ? i18n["smsc.stripAndReplace.aNumber.addTitle"] ||
            "Add A-Number Strip and Replace Entry"
        : i18n["smsc.stripAndReplace.bNumber.addTitle"] ||
            "Add B-Number Strip and Replace Entry";
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="lg" fullWidth>
      <DialogTitle>{getTitle()}</DialogTitle>
      <DialogContent>
        <ErrorsDisplay
          errorResponse={errorResponse}
          keyPrefix="smsws.stripAndReplace.form.keys"
        />

        <Accordion defaultExpanded className="boxed-accordion" sx={{ mb: 3, mt: 2 }}>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography variant="h6" className="accordion-title">
              {i18n["smsc.stripAndReplace.form.numbersToAnalyse"] || "Numbers to Analyze"}
            </Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Typography variant="body2" color="textSecondary" gutterBottom sx={{ mb: 3 }}>
              {i18n["smsc.stripAndReplace.form.numbersToAnalyseDescription"] ||
                "Only the Number portion (excluding TON and NPI) is used for Min/Max Digit calculations"}
            </Typography>

            <Box sx={{ display: "flex", gap: 3, justifyContent: "flex-start" }}>
              <TextField
                type="number"
                label={i18n["smsc.stripAndReplace.form.minDigits"] || "Min Digits"}
                value={formEntry.minDigits === undefined ? "" : formEntry.minDigits}
                onChange={(e) => {
                  const value = e.target.value === "" ? undefined : parseInt(e.target.value);
                  handleChange("minDigits", value);
                }}
                error={!!errors.minDigits}
                helperText={errors.minDigits as string}
                inputProps={{ min: 1, max: 20 }}
                className="smaller-number-input"
              />
              <TextField
                type="number"
                label={i18n["smsc.stripAndReplace.form.maxDigits"] || "Max Digits"}
                value={formEntry.maxDigits === undefined ? "" : formEntry.maxDigits}
                onChange={(e) => {
                  const value = e.target.value === "" ? undefined : parseInt(e.target.value);
                  handleChange("maxDigits", value);
                }}
                error={!!errors.maxDigits}
                helperText={errors.maxDigits as string}
                inputProps={{ min: 1, max: 20 }}
                className="smaller-number-input"
              />
            </Box>
          </AccordionDetails>
        </Accordion>

        <Accordion defaultExpanded className="boxed-accordion" sx={{ mb: 3 }}>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography variant="h6" className="accordion-title">
              {i18n["smsc.stripAndReplace.form.strip"] || "Strip"}
            </Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Typography variant="body2" color="textSecondary" gutterBottom>
              {i18n["smsc.stripAndReplace.form.stripDescription"] ||
                "Here you configure the portion of the number that will be stripped when it is encountered in an SMS."}
            </Typography>
            <Typography variant="body2" color="textSecondary" gutterBottom sx={{ mb: 3 }}>
              {i18n["smsc.stripAndReplace.form.selectAny"] ||
                'Select "Any" on TON/NPI dropdowns for wildcards. Use * in Number field for wildcard in a particular digit position.'}
            </Typography>

            <Box sx={{ display: "flex", justifyContent: "space-between", gap: 2 }}>
              <Box sx={{ width: "30%" }}>
                <FormControl fullWidth error={!!errors.stripTypeOfNumber}>
                  <InputLabel id="strip-ton-label">
                    {i18n["smsc.stripAndReplace.form.typeOfNumber"] ||
                      "Type Of Number (TON)"}
                  </InputLabel>
                  <Select
                    labelId="strip-ton-label"
                    value={formEntry.stripTypeOfNumber || "ANY"}
                    label={
                      i18n["smsc.stripAndReplace.form.typeOfNumber"] ||
                      "Type Of Number (TON)"
                    }
                    onChange={(e) => handleChange("stripTypeOfNumber", e.target.value)}
                    size="medium"
                  >
                    <MenuItem value="ANY">ANY</MenuItem>
                    {typeOfNumberOptions.map((option) => (
                      <MenuItem key={option.value} value={option.value}>
                        {option.displayText}
                      </MenuItem>
                    ))}
                  </Select>
                  {errors.stripTypeOfNumber && (
                    <FormHelperText>{errors.stripTypeOfNumber as string}</FormHelperText>
                  )}
                </FormControl>
              </Box>
              <Box sx={{ width: "30%" }}>
                <FormControl fullWidth error={!!errors.stripNumberPlanIndicator}>
                  <InputLabel id="strip-npi-label">
                    {i18n["smsc.stripAndReplace.form.numberingPlan"] ||
                      "Numbering Plan Indicator (NPI)"}
                  </InputLabel>
                  <Select
                    labelId="strip-npi-label"
                    value={formEntry.stripNumberPlanIndicator || "ANY"}
                    label={
                      i18n["smsc.stripAndReplace.form.numberingPlan"] ||
                      "Numbering Plan Indicator (NPI)"
                    }
                    onChange={(e) => handleChange("stripNumberPlanIndicator", e.target.value)}
                    size="medium"
                  >
                    <MenuItem value="ANY">ANY</MenuItem>
                    {numberPlanIndicatorOptions.map((option) => (
                      <MenuItem key={option.value} value={option.value}>
                        {option.displayText}
                      </MenuItem>
                    ))}
                  </Select>
                  {errors.stripNumberPlanIndicator && (
                    <FormHelperText>{errors.stripNumberPlanIndicator as string}</FormHelperText>
                  )}
                </FormControl>
              </Box>
              <Box sx={{ width: "30%" }}>
                <TextField
                  fullWidth
                  size="medium"
                  label={
                    i18n["smsc.stripAndReplace.form.number"] ||
                    "Number (* for wildcard)"
                  }
                  value={formEntry.stripNumber || ""}
                  onChange={(e) => handleChange("stripNumber", e.target.value)}
                  error={!!errors.stripNumber}
                  helperText={errors.stripNumber as string}
                />
              </Box>
            </Box>
          </AccordionDetails>
        </Accordion>

        <Accordion defaultExpanded className="boxed-accordion" sx={{ mb: 3 }}>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography variant="h6" className="accordion-title">
              {i18n["smsc.stripAndReplace.form.replace"] || "Replace"}
            </Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Typography variant="body2" color="textSecondary" gutterBottom>
              {i18n["smsc.stripAndReplace.form.replaceDescription"] ||
                "Here you configure the number that will replace the stripped portion."}
            </Typography>
            <Typography variant="body2" color="textSecondary" gutterBottom sx={{ mb: 3 }}>
              {i18n["smsc.stripAndReplace.form.selectKeep"] ||
                'Select "Keep" on TON and NPI to preserve their original values. Use * in Number field to preserve the original digit in that position.'}
            </Typography>

            <Box sx={{ display: "flex", justifyContent: "space-between", gap: 2 }}>
              <Box sx={{ width: "30%" }}>
                <FormControl fullWidth error={!!errors.replaceTypeOfNumber}>
                  <InputLabel id="replace-ton-label">
                    {i18n["smsc.stripAndReplace.form.typeOfNumber"] ||
                      "Type Of Number (TON)"}
                  </InputLabel>
                  <Select
                    labelId="replace-ton-label"
                    value={formEntry.replaceTypeOfNumber || "KEEP"}
                    label={
                      i18n["smsc.stripAndReplace.form.typeOfNumber"] ||
                      "Type Of Number (TON)"
                    }
                    onChange={(e) => handleChange("replaceTypeOfNumber", e.target.value)}
                    size="medium"
                  >
                    <MenuItem value="KEEP">KEEP</MenuItem>
                    {typeOfNumberOptions.map((option) => (
                      <MenuItem key={option.value} value={option.value}>
                        {option.displayText}
                      </MenuItem>
                    ))}
                  </Select>
                  {errors.replaceTypeOfNumber && (
                    <FormHelperText>{errors.replaceTypeOfNumber as string}</FormHelperText>
                  )}
                </FormControl>
              </Box>
              <Box sx={{ width: "30%" }}>
                <FormControl fullWidth error={!!errors.replaceNumberPlanIndicator}>
                  <InputLabel id="replace-npi-label">
                    {i18n["smsc.stripAndReplace.form.numberingPlan"] ||
                      "Numbering Plan Indicator (NPI)"}
                  </InputLabel>
                  <Select
                    labelId="replace-npi-label"
                    value={formEntry.replaceNumberPlanIndicator || "KEEP"}
                    label={
                      i18n["smsc.stripAndReplace.form.numberingPlan"] ||
                      "Numbering Plan Indicator (NPI)"
                    }
                    onChange={(e) => handleChange("replaceNumberPlanIndicator", e.target.value)}
                    size="medium"
                  >
                    <MenuItem value="KEEP">KEEP</MenuItem>
                    {numberPlanIndicatorOptions.map((option) => (
                      <MenuItem key={option.value} value={option.value}>
                        {option.displayText}
                      </MenuItem>
                    ))}
                  </Select>
                  {errors.replaceNumberPlanIndicator && (
                    <FormHelperText>{errors.replaceNumberPlanIndicator as string}</FormHelperText>
                  )}
                </FormControl>
              </Box>
              <Box sx={{ width: "30%" }}>
                <TextField
                  fullWidth
                  size="medium"
                  label={
                    i18n["smsc.stripAndReplace.form.number"] ||
                    "Number (* for wildcard)"
                  }
                  value={formEntry.replaceNumber || ""}
                  onChange={(e) => handleChange("replaceNumber", e.target.value)}
                />
              </Box>
            </Box>
          </AccordionDetails>
        </Accordion>
      </DialogContent>
      <DialogActions>
        <Button
          onClick={handleSubmit}
          color="primary"
          variant="contained"
          sx={{ mr: 1 }}
        >
          {isEditing
            ? i18n["button.update"] || "Update"
            : i18n["button.add"] || "Add"}
        </Button>
        <Button
          onClick={onClose}
          sx={{
            backgroundColor: '#e91e63', // Pink color
            '&:hover': {
              backgroundColor: '#ad1457', // Darker pink on hover
            }
          }}
          variant="contained"
        >
          {i18n["button.cancel"] || "Cancel"}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default StripAndReplaceFormDialog;
