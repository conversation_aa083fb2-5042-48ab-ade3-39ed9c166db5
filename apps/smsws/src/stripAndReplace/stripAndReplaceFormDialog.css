/* Strip and Replace Form Dialog Accordion Styling */

/* Accordion styling to match the original JavaScript version */
.MuiAccordion-root {
  border: 1px solid #e0e0e0 !important;
  border-radius: 4px !important;
  box-shadow: none !important;
  margin-bottom: 16px !important;
  background-color: #ffffff !important;
}

.MuiAccordion-root:before {
  display: none !important;
}

.MuiAccordionSummary-root {
  background-color: #ffffff !important;
  border-bottom: 1px solid #e0e0e0 !important;
  min-height: 48px !important;
  padding: 0 16px !important;
}

.MuiAccordionSummary-root:hover {
  background-color: #f5f5f5 !important;
}

.MuiAccordionSummary-root.Mui-expanded {
  min-height: 48px !important;
  border-bottom: 1px solid #e0e0e0 !important;
}

.MuiAccordionSummary-content {
  margin: 12px 0 !important;
}

.MuiAccordionSummary-content.Mui-expanded {
  margin: 12px 0 !important;
}

.MuiAccordionSummary-expandIconWrapper {
  color: #666666 !important;
}

.MuiAccordionDetails-root {
  padding: 16px !important;
  background-color: #ffffff !important;
  border-top: none !important;
}

/* Accordion title styling */
.accordion-title {
  font-weight: 500 !important;
  color: #333333 !important;
  font-size: 1rem !important;
}

/* Remove default Material UI accordion styling that we don't want */
.MuiAccordion-root.Mui-expanded {
  margin: 0 0 16px 0 !important;
}

.MuiAccordion-root.MuiAccordion-rounded {
  border-radius: 4px !important;
}

.MuiAccordion-root.MuiAccordion-rounded:first-of-type {
  border-radius: 4px !important;
}

.MuiAccordion-root.MuiAccordion-rounded:last-of-type {
  border-radius: 4px !important;
}

/* Boxed accordion styling to match number analysis page */
.MuiAccordion-root.boxed-accordion {
  border: 1px solid #e0e0e0 !important;
  border-radius: 8px !important;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
  background-color: #fff !important;
  margin-top: 1.5em !important;
  margin-bottom: 1.5em !important;
}

.MuiAccordion-root.boxed-accordion:before {
  display: none !important;
}

.MuiAccordion-root.boxed-accordion.Mui-expanded {
  margin: 1.5em 0 !important;
}

.MuiAccordion-root.boxed-accordion .MuiAccordionSummary-root {
  padding: 16px !important;
  background-color: white !important;
}

.MuiAccordion-root.boxed-accordion .MuiAccordionDetails-root {
  padding: 16px !important;
}
