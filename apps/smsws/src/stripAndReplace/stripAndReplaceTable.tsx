import React, { useState, useEffect } from 'react';
import {
  DragDropContext,
  Draggable,
  Droppable,
  DropResult,
} from "@hello-pangea/dnd";
import {
  Checkbox,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
} from "@mui/material";
import { StripAndReplaceEntry } from '@pnmui/common/types/smscTypes';

interface StripAndReplaceTableProps {
  entries: StripAndReplaceEntry[];
  onRowClick: (entry: StripAndReplaceEntry) => void;
  onRowOrderChange: (newEntries: StripAndReplaceEntry[]) => void;
  onSelectionChange: (selectedEntries: StripAndReplaceEntry[]) => void;
  i18n: Record<string, string>;
}

// Helper function to convert enum values to numeric codes for display
const getTypeOfNumberCode = (ton: string | undefined): string => {
  switch (ton) {
    case 'UNKNOWN': return '00';
    case 'INTERNATIONAL': return '01';
    case 'NATIONAL': return '02';
    case 'NETWORK_SPECIFIC': return '03';
    case 'SUBSCRIBER_NUMBER': return '04';
    case 'ALPHANUMERIC': return '05';
    case 'ABBREVIATED': return '06';
    default: return '';
  }
};

const getNumberPlanIndicatorCode = (npi: string | undefined): string => {
  switch (npi) {
    case 'UNKNOWN': return '00';
    case 'ISDN': return '01';
    case 'DATA': return '03';
    case 'TELEX': return '04';
    case 'LAND_MOBILE': return '06';
    case 'NATIONAL': return '08';
    case 'PRIVATE': return '09';
    case 'ERMES': return '10';
    case 'INTERNET': return '14';
    case 'WAP_CLIENT_ID': return '18';
    default: return '';
  }
};

// Helper function to format the portion of number to strip display
const formatPortionOfNumber = (entry: StripAndReplaceEntry): string => {
  const parts: string[] = [];

  // Add TON code if available and not "ANY"
  if (entry.stripTypeOfNumber && entry.stripTypeOfNumber !== "ANY") {
    const tonCode = getTypeOfNumberCode(entry.stripTypeOfNumber);
    if (tonCode) parts.push(tonCode);
  }

  // Add NPI code if available and not "ANY"
  if (entry.stripNumberPlanIndicator && entry.stripNumberPlanIndicator !== "ANY") {
    const npiCode = getNumberPlanIndicatorCode(entry.stripNumberPlanIndicator);
    if (npiCode) parts.push(npiCode);
  }

  // Add the strip number if available
  if (entry.stripNumber) {
    parts.push(entry.stripNumber);
  }

  return parts.join(',');
};

// Helper function to format the replace with display
const formatReplaceWith = (entry: StripAndReplaceEntry): string => {
  const parts: string[] = [];

  // Add TON code if available and not "KEEP"
  if (entry.replaceTypeOfNumber && entry.replaceTypeOfNumber !== "KEEP") {
    const tonCode = getTypeOfNumberCode(entry.replaceTypeOfNumber);
    if (tonCode) parts.push(tonCode);
  }

  // Add NPI code if available and not "KEEP"
  if (entry.replaceNumberPlanIndicator && entry.replaceNumberPlanIndicator !== "KEEP") {
    const npiCode = getNumberPlanIndicatorCode(entry.replaceNumberPlanIndicator);
    if (npiCode) parts.push(npiCode);
  }

  // Add the replace number if available
  if (entry.replaceNumber) {
    parts.push(entry.replaceNumber);
  }

  return parts.join(',');
};

const StripAndReplaceTable: React.FC<StripAndReplaceTableProps> = ({
  entries,
  onRowClick,
  onRowOrderChange,
  onSelectionChange,
  i18n,
}) => {
  const [selectedEntries, setSelectedEntries] = useState<string[]>([]);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(20);

  // Handle pagination
  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Handle drag and drop
  const handleDragEnd = (result: DropResult) => {
    if (!result.destination) return;

    const items = Array.from(entries);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    onRowOrderChange(items);
  };

  // Handle checkbox selection
  const handleSelectRow = (id: string, isSelected: boolean) => {
    let newSelectedEntries: string[];

    if (isSelected) {
      newSelectedEntries = [...selectedEntries, id];
    } else {
      newSelectedEntries = selectedEntries.filter(entryId => entryId !== id);
    }

    setSelectedEntries(newSelectedEntries);

    // Convert IDs back to entries for the parent component
    // For entries without IDs, we need to match by index
    const selectedEntriesObjects = entries.filter((entry, index) => {
      const entryId = entry.id ? String(entry.id) : `temp-${index}-${Date.now()}`;
      return newSelectedEntries.some(id => {
        // If the ID starts with 'temp-', extract the index part and compare
        if (id.startsWith('temp-')) {
          const idParts = id.split('-');
          const idIndex = parseInt(idParts[1], 10);
          return idIndex === index;
        }
        // Otherwise, compare by actual ID
        return id === String(entry.id);
      });
    });

    onSelectionChange(selectedEntriesObjects);
  };

  // Handle row click (for editing)
  const handleRowClick = (event: React.MouseEvent, entry: StripAndReplaceEntry) => {
    // Check if the click was on a checkbox or drag handle
    const target = event.target as HTMLElement;
    const closestCheckbox = target.closest('.MuiCheckbox-root');
    const isDragHandle = target.getAttribute('data-drag-handle') === 'true';

    if (closestCheckbox || isDragHandle) {
      return;
    }

    onRowClick(entry);
  };

  // Calculate which rows to display based on pagination
  const displayedEntries = entries.slice(
    page * rowsPerPage,
    page * rowsPerPage + rowsPerPage
  );

  return (
    <Paper elevation={2} style={{ width: '100%' }}>
      <TableContainer>
        <DragDropContext onDragEnd={handleDragEnd}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell padding="checkbox" style={{ width: '50px' }}>
                  {/* Checkbox header */}
                </TableCell>
                <TableCell style={{ width: '50px' }}>
                  {/* Drag handle header */}
                </TableCell>
                <TableCell>
                  {i18n["smsc.stripAndReplace.portionOfNumber"] || "Portion of Number to Strip"}
                </TableCell>
                <TableCell>
                  {i18n["smsc.stripAndReplace.replaceWith"] || "Replace with"}
                </TableCell>
                <TableCell>
                  {i18n["smsc.stripAndReplace.minDigits"] || "Min Digits"}
                </TableCell>
                <TableCell>
                  {i18n["smsc.stripAndReplace.maxDigits"] || "Max Digits"}
                </TableCell>
              </TableRow>
            </TableHead>
            <Droppable droppableId="strip-and-replace-table">
              {(provided) => (
                <TableBody
                  {...provided.droppableProps}
                  ref={provided.innerRef}
                >
                  {displayedEntries.map((entry, index) => {
                    // Generate a temporary ID for entries without an ID
                    const entryId = entry.id ? String(entry.id) : `temp-${index}-${Date.now()}`;
                    const isSelected = selectedEntries.includes(entryId);

                    return (
                      <Draggable
                        key={entryId}
                        draggableId={entryId}
                        index={index}
                      >
                        {(providedDrag) => (
                          <TableRow
                            ref={providedDrag.innerRef}
                            {...providedDrag.draggableProps}
                            hover
                            role="checkbox"
                            aria-checked={isSelected}
                            tabIndex={-1}
                            selected={isSelected}
                            onClick={(event) => handleRowClick(event, entry)}
                            style={{
                              ...providedDrag.draggableProps.style,
                              cursor: 'pointer',
                            }}
                          >
                            {/* Checkbox cell */}
                            <TableCell padding="checkbox">
                              <Checkbox
                                checked={isSelected}
                                onChange={(e) => handleSelectRow(entryId, e.target.checked)}
                                onClick={(e) => e.stopPropagation()}
                                inputProps={{ 'aria-labelledby': `enhanced-table-checkbox-${index}` }}
                              />
                            </TableCell>

                            {/* Drag handle cell */}
                            <TableCell
                              {...providedDrag.dragHandleProps}
                              style={{ cursor: 'grab', width: '50px' }}
                              data-drag-handle="true"
                            >
                              <span style={{ fontSize: '24px', fontWeight: 'bold' }}>☰</span>
                            </TableCell>

                            {/* Data cells */}
                            <TableCell>{formatPortionOfNumber(entry)}</TableCell>
                            <TableCell>{formatReplaceWith(entry)}</TableCell>
                            <TableCell>{entry.minDigits}</TableCell>
                            <TableCell>{entry.maxDigits}</TableCell>
                          </TableRow>
                        )}
                      </Draggable>
                    );
                  })}
                  {provided.placeholder}
                </TableBody>
              )}
            </Droppable>
          </Table>
        </DragDropContext>
      </TableContainer>

      <TablePagination
        rowsPerPageOptions={[10, 20, 50]}
        component="div"
        count={entries.length}
        rowsPerPage={rowsPerPage}
        page={page}
        onPageChange={handleChangePage}
        onRowsPerPageChange={handleChangeRowsPerPage}
      />
    </Paper>
  );
};

export default StripAndReplaceTable;
