// Remove icon imports as we'll use text buttons like in applications page
import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  CardHeader,
  FormControlLabel,
  MenuItem,
  Switch,
  Typography,
} from "@mui/material";
import { MaterialTable } from "@pnmui/common/components";
import { $i18n, $properties } from "@pnmui/common/services/propertiesService";
import { SmscStripAndReplaceService } from "@pnmui/common/services/smsc/stripAndReplaceService";
import {
  StripAndReplace,
  StripAndReplaceEntry,
  StripAndReplaceType,
} from "@pnmui/common/types/smscTypes";
import StripAndReplaceTable from "./stripAndReplaceTable";
import React, { useEffect, useState } from "react";
import {
  SelectValidator,
  ValidatorForm,
} from "react-material-ui-form-validator";
import { useNavigate, useParams } from "react-router-dom";
import { toast } from "react-toastify";
import { Subscription } from "rxjs";
import { tap } from "rxjs/operators";
import StripAndReplaceFormDialog from "./stripAndReplaceFormDialog";

// Add window.properties type declaration
declare global {
  interface Window {
    properties?: Record<string, string>;
  }
}

const SmscStripAndReplace = () => {
  const navigate = useNavigate();
  const params = useParams<{ type: string }>();
  const [i18n, setI18n] = useState<Record<string, string>>({});
  const [stripAndReplace, setStripAndReplace] =
    useState<StripAndReplace | null>(null);
  const [selectedEntries, setSelectedEntries] = useState<
    StripAndReplaceEntry[]
  >([]);
  const [type, setType] = useState<StripAndReplaceType>(
    params.type === "b-number"
      ? StripAndReplaceType.B_NUMBER
      : StripAndReplaceType.A_NUMBER
  );
  const [originalEntries, setOriginalEntries] = useState<
    StripAndReplaceEntry[]
  >([]);
  const [filteredEntries, setFilteredEntries] = useState<
    StripAndReplaceEntry[]
  >([]);
  const [selectedStripNumber, setSelectedStripNumber] = useState<string>("");
  const [dialogOpen, setDialogOpen] = useState<boolean>(false);
  const [currentEntry, setCurrentEntry] = useState<StripAndReplaceEntry | undefined>(undefined);
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState<boolean>(false);

  useEffect(() => {
    // Subscribe to i18n properties
    const i18nSubscription: Subscription = $i18n
      .pipe(tap())
      .subscribe((i18NProps) => {
        if (i18NProps) {
          setI18n(i18NProps);
        }
      });

    loadData();

    return () => {
      i18nSubscription.unsubscribe();
    };
  }, [params.type]);

  // Update type when params change
  useEffect(() => {
    setType(
      params.type === "b-number"
        ? StripAndReplaceType.B_NUMBER
        : StripAndReplaceType.A_NUMBER
    );
  }, [params.type]);

  const loadData = () => {
    const currentType =
      params.type === "b-number"
        ? StripAndReplaceType.B_NUMBER
        : StripAndReplaceType.A_NUMBER;

    console.log("[DEBUG] Starting to load Strip and Replace data for type:", currentType);
    console.log("[DEBUG] Current base URL:", $properties.value["client.smsws.base.url"]);

    // No hardcoded data - we'll use the service for both A-number and B-number

    SmscStripAndReplaceService.getStripAndReplaceByType(currentType)
      .then((data) => {
        console.log("[DEBUG] Loaded Strip and Replace data:", data);
        console.log("[DEBUG] Data has entries:", data.stripAndReplaceEntries ? data.stripAndReplaceEntries.length : 0);
        console.log("[DEBUG] Raw entries:", data.stripAndReplaceEntries);

        // Ensure entries are sorted by order if available, otherwise keep original order
        const sortedEntries = (data.stripAndReplaceEntries || [])
          .filter((entry) => {
            console.log("[DEBUG] Processing entry:", entry);
            return entry != null;
          })
          .sort((a, b) => (a.order ?? Infinity) - (b.order ?? Infinity))
          // Ensure all IDs are strings for drag and drop functionality
          .map(entry => ({
            ...entry,
            id: entry.id ? String(entry.id) : `temp-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
          }));

        console.log("[DEBUG] Sorted entries:", sortedEntries);
        console.log("[DEBUG] Sorted entries length:", sortedEntries.length);

        setStripAndReplace({ ...data, stripAndReplaceEntries: sortedEntries });
        setOriginalEntries(sortedEntries);
        setFilteredEntries(sortedEntries); // Initialize filtered with sorted

        console.log("[DEBUG] State updated with entries:", sortedEntries.length);
      })
      .catch((error) => {
        console.error("Error loading Strip and Replace data:", error);
        toast.error("Failed to load Strip and Replace data: " + error.message);
        // Create a new empty object if none exists
        const emptyData = {
          type: currentType,
          moStripAndReplaceEnabled: false,
          aoStripAndReplaceEnabled: false,
          stripAndReplaceEntries: [],
        };
        setStripAndReplace(emptyData);
        setOriginalEntries([]);
        setFilteredEntries([]);
      });
  };

  const handleMoEnabledChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    if (stripAndReplace) {
      const newValue = event.target.checked;

      // Update the UI only, don't submit to server
      setStripAndReplace({
        ...stripAndReplace,
        moStripAndReplaceEnabled: newValue,
      });

      // Mark that we have unsaved changes
      setHasUnsavedChanges(true);
    }
  };

  const handleAoEnabledChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    if (stripAndReplace) {
      const newValue = event.target.checked;

      // Update the UI only, don't submit to server
      setStripAndReplace({
        ...stripAndReplace,
        aoStripAndReplaceEnabled: newValue,
      });

      // Mark that we have unsaved changes
      setHasUnsavedChanges(true);
    }
  };

  const handleAddEntry = () => {
    setCurrentEntry(undefined);
    setIsEditing(false);
    setDialogOpen(true);
  };

  const handleEditEntry = (entry: StripAndReplaceEntry) => {
    setCurrentEntry(entry);
    setIsEditing(true);
    setDialogOpen(true);
  };

  const handleDialogClose = () => {
    setDialogOpen(false);
    setCurrentEntry(undefined);
  };

  const handleSaveEntry = (entry: StripAndReplaceEntry) => {
    if (!stripAndReplace) return;

    // If editing, update the existing entry
    if (isEditing && currentEntry) {
      const updatedEntries = originalEntries.map(e =>
        e.id === entry.id ? entry : e
      );

      // Update local state
      setOriginalEntries(updatedEntries);
      setFilteredEntries(
        updatedEntries.filter(
          (e) =>
            selectedStripNumber === "" ||
            selectedStripNumber.toLowerCase() === "any" ||
            e.stripNumber
              ?.toLowerCase()
              .includes(selectedStripNumber.toLowerCase())
        )
      );

      setStripAndReplace({
        ...stripAndReplace,
        stripAndReplaceEntries: updatedEntries,
      });

      setHasUnsavedChanges(true);
      // Removed toast notification when returning from modal
    }
    // If adding, add the new entry with a temporary ID for UI functionality
    else {
      // Assign a temporary ID with "new-" prefix for UI functionality
      // This will be removed before sending to the server
      const newEntry = {
        ...entry,
        id: `new-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
      };

      const updatedEntries = [newEntry, ...originalEntries];

      // Update local state
      setOriginalEntries(updatedEntries);
      setFilteredEntries(
        updatedEntries.filter(
          (e) =>
            selectedStripNumber === "" ||
            selectedStripNumber.toLowerCase() === "any" ||
            e.stripNumber
              ?.toLowerCase()
              .includes(selectedStripNumber.toLowerCase())
        )
      );

      setStripAndReplace({
        ...stripAndReplace,
        stripAndReplaceEntries: updatedEntries,
      });

      setHasUnsavedChanges(true);
      // Removed toast notification when returning from modal
    }
  };

  const handleDeleteEntries = () => {
    if (!stripAndReplace) return;

    // If no entries are selected, show a message and return
    if (!selectedEntries.length) {
      toast.info("Please select entries to delete");
      return;
    }

    // Filter originalEntries to get the remaining ones after deletion
    const remainingOriginalEntries = originalEntries.filter(
      (entry) => !selectedEntries.some((selected) => selected.id === entry.id)
    );

    // Update the order based on the new array
    const updatedEntriesWithOrder = remainingOriginalEntries.map(
      (entry, index) => ({
        ...entry,
        order: index, // Assign new order based on position after deletion
      })
    );

    const updatedStripAndReplace = {
      ...stripAndReplace,
      stripAndReplaceEntries: updatedEntriesWithOrder,
    };

    console.log(
      `Deleting entries and reordering with data:`,
      updatedStripAndReplace
    );

    // Update UI only, don't submit to server
    setOriginalEntries(updatedEntriesWithOrder);
    setFilteredEntries(
      updatedEntriesWithOrder.filter(
        (entry) =>
          selectedStripNumber === "" ||
          selectedStripNumber.toLowerCase() === "any" ||
          entry.stripNumber
            ?.toLowerCase()
            .includes(selectedStripNumber.toLowerCase())
      )
    ); // Update filtered entries based on current filter
    setStripAndReplace(updatedStripAndReplace);
    setSelectedEntries([]); // Clear selection

    // Mark that we have unsaved changes
    setHasUnsavedChanges(true);
    toast.success("Entries deleted successfully");
  };

  // Add search function similar to applications page
  const search = () => {
    if (
      selectedStripNumber === "" ||
      selectedStripNumber.toLowerCase() === "any"
    ) {
      setFilteredEntries(originalEntries);
    } else {
      const filtered = originalEntries.filter((entry) =>
        entry.stripNumber
          ?.toLowerCase()
          .includes(selectedStripNumber.toLowerCase())
      );
      setFilteredEntries(filtered);
    }
  };

  // Add useEffect to trigger search when selectedStripNumber changes
  useEffect(() => {
    search();
  }, [selectedStripNumber]);

  // Add Handler for Drag and Drop Reordering
  const handleRowOrderChange = (newOrderedEntries: StripAndReplaceEntry[]) => {
    if (!stripAndReplace) return;

    // Assign the new order based on the array index
    const updatedEntriesWithOrder = newOrderedEntries.map((entry, index) => ({
      ...entry,
      order: index,
    }));

    const updatedStripAndReplace = {
      ...stripAndReplace,
      stripAndReplaceEntries: updatedEntriesWithOrder,
    };

    // Update local state only, don't submit to server
    setOriginalEntries(updatedEntriesWithOrder);
    setFilteredEntries(
      updatedEntriesWithOrder.filter(
        (entry) =>
          selectedStripNumber === "" ||
          selectedStripNumber.toLowerCase() === "any" ||
          entry.stripNumber
            ?.toLowerCase()
            .includes(selectedStripNumber.toLowerCase())
      )
    ); // Update filtered entries based on current filter
    setStripAndReplace(updatedStripAndReplace);

    // Mark that we have unsaved changes
    setHasUnsavedChanges(true);
    // Removed toast notification for row reordering
  };

  const getTitle = () => {
    return type === StripAndReplaceType.A_NUMBER
      ? i18n["smsc.stripAndReplace.aNumber.title"] ||
          "Strip And Replace A-Number"
      : i18n["smsc.stripAndReplace.bNumber.title"] ||
          "Strip And Replace B-Number";
  };

  const handleSubmit = () => {
    if (!stripAndReplace) return;

    // Create a deep copy of the stripAndReplace object
    const dataToSubmit = JSON.parse(JSON.stringify(stripAndReplace));

    // Clean up the payload for server submission
    if (dataToSubmit.stripAndReplaceEntries && Array.isArray(dataToSubmit.stripAndReplaceEntries)) {
      dataToSubmit.stripAndReplaceEntries = dataToSubmit.stripAndReplaceEntries.map(entry => {
        // Create a clean entry object - NEVER include ID in server payload
        const cleanEntry: any = {};

        // Copy required fields
        if (entry.stripNumber) cleanEntry.stripNumber = entry.stripNumber;
        if (entry.replaceNumber !== undefined) cleanEntry.replaceNumber = entry.replaceNumber;
        if (entry.minDigits !== undefined) cleanEntry.minDigits = entry.minDigits;
        if (entry.maxDigits !== undefined) cleanEntry.maxDigits = entry.maxDigits;
        if (entry.order !== undefined) cleanEntry.order = entry.order;

        // Handle TON and NPI fields - keep as they are (null values are valid)
        cleanEntry.stripTypeOfNumber = entry.stripTypeOfNumber;
        cleanEntry.stripNumberPlanIndicator = entry.stripNumberPlanIndicator;
        cleanEntry.replaceTypeOfNumber = entry.replaceTypeOfNumber;
        cleanEntry.replaceNumberPlanIndicator = entry.replaceNumberPlanIndicator;

        // Don't include server-managed fields:
        // - id (server assigns IDs)
        // - createdTime and lastUpdatedTime (server manages timestamps)

        return cleanEntry;
      });
    }

    // Remove server-managed timestamp fields from the main object
    const { createdTime, lastUpdatedTime, ...cleanDataToSubmit } = dataToSubmit;

    console.log("Submitting cleaned data (no IDs):", cleanDataToSubmit);

    // Submit all changes to the server
    SmscStripAndReplaceService.updateStripAndReplace(cleanDataToSubmit)
      .then((data) => {
        console.log("Strip and Replace configuration saved successfully:", data);

        // Refresh data from server to get server-assigned IDs
        loadData();

        setHasUnsavedChanges(false);
        toast.success("Strip and Replace configuration saved successfully");
      })
      .catch((error) => {
        console.error("Error saving Strip and Replace configuration:", error);
        toast.error(
          "Failed to save Strip and Replace configuration: " +
          (error.message || "Unknown error")
        );
      });
  };

  const handleBack = () => {
    // Navigate back to the previous page
    navigate(-1);
  };

  return (
    <div className="wrapper">
      <Card className="content-card">
        {/* Add search filter similar to applications page */}
        <div className="form-row">
          <div style={{ display: "flex", alignItems: "center" }}>

          </div>
        </div>

        {/* Add switches for MO and AO enabled */}
        <div
          style={{ display: "flex", marginBottom: "20px", marginLeft: "15px" }}
        >
          <FormControlLabel
            control={
              <Switch
                checked={stripAndReplace?.moStripAndReplaceEnabled || false}
                onChange={handleMoEnabledChange}
                color="primary"
              />
            }
            label={
              i18n["smsc.stripAndReplace.moEnabled"] ||
              "MO(P2A/P2P) Strip and Replace enabled"
            }
            style={{ marginRight: "40px" }}
          />
          <FormControlLabel
            control={
              <Switch
                checked={stripAndReplace?.aoStripAndReplaceEnabled || false}
                onChange={handleAoEnabledChange}
                color="primary"
              />
            }
            label={
              i18n["smsc.stripAndReplace.aoEnabled"] ||
              "AO(A2P) Strip and Replace enabled"
            }
          />
        </div>

        <CardContent>
          <Typography variant="body2" color="textSecondary" gutterBottom>
            {i18n["smsc.stripAndReplace.orderImportant"] ||
              "Here you create Strip and Replace Configurations based on A-Number of the SMS, Drag and Drop to reorder rows"}
          </Typography>

          <br/>

          {/* Use our custom StripAndReplaceTable component */}
          <StripAndReplaceTable
            entries={filteredEntries}
            onRowClick={handleEditEntry}
            onRowOrderChange={handleRowOrderChange}
            onSelectionChange={setSelectedEntries}
            i18n={i18n}
          />
        </CardContent>

        {/* Move buttons to bottom of card like in applications page */}
        <Button
          variant="contained"
          color="primary"
          className="request-handler-add-button"
          aria-label="Add"
          onClick={handleAddEntry}
          style={{
            marginLeft: "15px",
            marginTop: "2rem",
            marginBottom: "1rem",
          }}
        >
          {i18n["button.add"] || "Add"}
        </Button>

        <span style={{ marginLeft: "10px" }}>
          <Button
            variant="contained"
            color="secondary"
            type="button"
            onClick={handleDeleteEntries}
            style={{ marginTop: "2rem", marginBottom: "1rem" }}
            className="request-handler-add-button"
          >
            {i18n["button.delete"] || "Delete"}
          </Button>
        </span>

        {/* Add Submit and Back buttons */}
        <div style={{ display: 'flex', justifyContent: 'flex-end', margin: '20px 15px' }}>
          <Button
              variant="contained"
              color="primary"
              onClick={handleSubmit}
              disabled={!hasUnsavedChanges}
            >
            {i18n["button.submit"] || "Submit"}
          </Button>
          <Button
            variant="contained"
            color="secondary"
            onClick={handleBack}
            sx={{ ml: 2 }}
          >
            {i18n["button.back"] || "Back"}
          </Button>
        </div>
      </Card>

      {/* Add the dialog component */}
      <StripAndReplaceFormDialog
        open={dialogOpen}
        onClose={handleDialogClose}
        onSave={handleSaveEntry}
        entry={currentEntry}
        type={type}
        isEditing={isEditing}
      />
    </div>
  );
};

export default SmscStripAndReplace;
