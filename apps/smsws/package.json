{"name": "smsws", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "dev:mf": "webpack serve --mode development --config webpack.config.cjs", "build:mf": "webpack --mode production --config webpack.config.cjs", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "playwright test", "test:ui": "playwright test --ui", "test:debug": "playwright test --debug", "test:report": "playwright show-report"}, "dependencies": {"@emotion/cache": "^11.14.0", "@emotion/is-prop-valid": "^1.3.1", "@emotion/memoize": "^0.9.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@emotion/unitless": "^0.10.0", "@hello-pangea/dnd": "^18.0.1", "@module-federation/error-codes": "^0.15.0", "@module-federation/runtime-core": "^0.15.0", "@module-federation/sdk": "^0.15.0", "@mui/icons-material": "^7.0.2", "@mui/material": "7.0.2", "@mui/styled-engine": "^7.0.1", "@mui/system": "^7.0.2", "@mui/utils": "^7.1.1", "@pnmui/common": "*", "@progress/kendo-data-query": "^1.6.0", "@progress/kendo-drawing": "^1.17.5", "@progress/kendo-licensing": "^1.3.1", "@progress/kendo-react-animation": "^5.16.1", "@progress/kendo-react-buttons": "^5.16.1", "@progress/kendo-react-common": "^11.1.0", "@progress/kendo-react-data-tools": "^5.16.1", "@progress/kendo-react-dateinputs": "^5.16.1", "@progress/kendo-react-dropdowns": "^5.16.1", "@progress/kendo-react-form": "^5.16.1", "@progress/kendo-react-grid": "^5.16.1", "@progress/kendo-react-inputs": "^5.16.1", "@progress/kendo-react-intl": "^5.16.1", "@progress/kendo-react-layout": "^5.16.1", "@progress/kendo-react-popup": "^5.16.1", "@progress/kendo-react-progressbars": "^5.16.1", "@progress/kendo-svg-icons": "^2.0.0", "@progress/kendo-theme-default": "^6.7.0", "@progress/kendo-theme-material": "^6.7.0", "asynckit": "^0.4.0", "builtin-status-codes": "^3.0.0", "clsx": "^2.1.1", "combined-stream": "^1.0.8", "cookie": "^1.0.2", "dayjs": "^1.11.10", "es-set-tostringtag": "^2.1.0", "hoist-non-react-statics": "^3.3.2", "inherits": "^2.0.4", "is-arguments": "^1.2.0", "is-generator-function": "^1.1.0", "is-typed-array": "^1.1.15", "material-ui-confirm": "^4.0.0", "mime-types": "^3.0.1", "punycode": "^2.3.1", "qs": "^6.14.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-form-validator-core": "^2.0.2", "react-is": "^19.1.0", "react-material-ui-form-validator": "^4.0.2", "react-router": "^7.6.2", "react-router-dom": "^7.5.0", "react-toastify": "^11.0.5", "readable-stream": "^4.7.0", "scheduler": "^0.26.0", "set-cookie-parser": "^2.7.1", "shallowequal": "^1.1.0", "stylis": "^4.3.6", "tslib": "^2.8.1", "which-typed-array": "^1.1.19", "xtend": "^4.0.2"}, "devDependencies": {"@emotion/sheet": "^1.4.0", "@emotion/weak-memoize": "^0.4.0", "@module-federation/enhanced": "^0.14.3", "@playwright/test": "^1.52.0", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "assert": "^2.1.0", "available-typed-arrays": "^1.0.7", "browserify-zlib": "^0.2.0", "buffer": "^6.0.3", "call-bind": "^1.0.8", "call-bound": "^1.0.4", "constants-browserify": "^1.0.0", "crypto-browserify": "^3.12.1", "css-loader": "^7.1.2", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "events": "^3.3.0", "for-each": "^0.3.5", "get-proto": "^1.0.1", "gopd": "^1.2.0", "has-tostringtag": "^1.0.2", "hasown": "^2.0.2", "html-webpack-plugin": "^5.6.3", "http-proxy-middleware": "^3.0.3", "https-browserify": "^1.0.0", "os-browserify": "^0.3.0", "path-browserify": "^1.0.1", "process": "^0.11.10", "prop-types": "^15.8.1", "querystring-es3": "^0.2.1", "stream-browserify": "^3.0.0", "stream-http": "^3.2.0", "style-loader": "^4.0.0", "ts-loader": "^9.5.2", "typescript": "^5.3.3", "url": "^0.11.4", "util": "^0.12.5", "vite": "^5.0.8", "vm-browserify": "^1.1.2", "webpack": "^5.99.9", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.2.1"}}