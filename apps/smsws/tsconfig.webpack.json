{"extends": "./tsconfig.json", "compilerOptions": {"noEmit": false, "allowImportingTsExtensions": false, "moduleResolution": "node", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "rootDir": "../../", "skipLibCheck": true, "strict": false, "noImplicitAny": false, "noUnusedLocals": false, "noUnusedParameters": false, "exactOptionalPropertyTypes": false, "noImplicitReturns": false, "noFallthroughCasesInSwitch": false, "noUncheckedIndexedAccess": false}, "include": ["src/**/*", "../../packages/common/src/**/*"]}