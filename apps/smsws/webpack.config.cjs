const { ModuleFederationPlugin } = require('@module-federation/enhanced');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const webpack = require('webpack');
const path = require('path');

module.exports = {
  mode: 'development',
  entry: './src/main.tsx',
  devtool: 'eval-source-map',

  output: {
    publicPath: 'auto'
  },

  resolve: {
    extensions: ['.mjs', '.js', '.jsx', '.ts', '.tsx', '.json'],
    alias: {
      '@pnmui/common': path.resolve(__dirname, '../../packages/common/src'),
      '@pnmui/common/*': path.resolve(__dirname, '../../packages/common/src/*'),
    },
  },

  resolveLoader: {
    modules: [
      path.resolve(__dirname, 'node_modules'),
      path.resolve(__dirname, '../../node_modules'),
      'node_modules'
    ]
  },

  module: {
    rules: [
      {
        test: /\.tsx?$/,
        use: {
          loader: 'ts-loader',
          options: {
            configFile: 'tsconfig.webpack.json',
            transpileOnly: true, // Skip type checking for faster builds
            compilerOptions: {
              noEmit: false,
              isolatedModules: true
            }
          }
        },
        exclude: /node_modules/,
      },
      {
        test: /\.m?jsx?$/,
        resolve: {
          fullySpecified: false // disable the behaviour for .mjs files
        },
        include: /node_modules/,
        type: 'javascript/auto'
      },
      {
        test: /\.m?js$/,
        include: /node_modules/,
        type: 'javascript/auto'
      },
      {
        test: /\.css$/,
        use: ['style-loader', 'css-loader'],
      },
      {
        test: /\.(png|jpe?g|gif|svg)$/,
        type: 'asset/resource',
      },
    ],
  },

  plugins: [
    // Provide process globally
    new webpack.ProvidePlugin({
      process: 'process/browser',
      Buffer: ['buffer', 'Buffer'],
    }),
    
    new ModuleFederationPlugin({
        name: 'smsws',
        filename: 'remoteEntry.js',
        dts: false, // Disable DTS generation to avoid the error
        exposes: {
          // Expose micro frontend components
          './MicroFrontend': './src/micro-frontend.tsx',
          // Expose the full app with layout for standalone mode
          './App': './src/App.tsx',
          // Expose app content without router wrapper
          './AppContent': './src/AppContent.tsx',
        },
        shared: {
          react: {
            singleton: true,
            requiredVersion: '^18.2.0',
            eager: true,
          },
          'react-dom': {
            singleton: true,
            requiredVersion: '^18.2.0',
            eager: true,
          },
          'react-router-dom': {
            singleton: true,
            requiredVersion: '^7.5.0',
            eager: true,
          },
          '@mui/material': {
            singleton: true,
            requiredVersion: '^7.0.2',
            version: '7.0.2',
            eager: true,
          },
          '@mui/icons-material': {
            singleton: true,
            requiredVersion: '^7.0.0',
            eager: true,
          },
          '@mui/system': {
            singleton: true,
            requiredVersion: '^7.0.1',
            version: '7.1.1',
            eager: true,
          },
          '@mui/styled-engine': {
            singleton: true,
            requiredVersion: '^7.0.1',
            version: '7.1.1',
            eager: true,
          },
          '@mui/utils': {
            singleton: true,
            requiredVersion: '^7.0.1',
            version: '7.1.1',
            eager: true,
          },
          '@emotion/react': {
            singleton: true,
            requiredVersion: '^11.14.0',
            eager: true,
          },
          '@emotion/styled': {
            singleton: true,
            requiredVersion: '^11.14.0',
            eager: true,
          },
          '@emotion/cache': {
            singleton: true,
            requiredVersion: '^11.14.0',
            eager: true,
          },
          'prop-types': {
            singleton: true,
            requiredVersion: '^15.8.0',
            eager: true,
          },
          // Kendo React packages
          '@progress/kendo-react-common': {
            singleton: true,
            eager: true,
          },
          '@progress/kendo-react-grid': {
            singleton: true,
            eager: true,
          },
          '@progress/kendo-react-data-tools': {
            singleton: true,
            eager: true,
          },
          '@progress/kendo-react-inputs': {
            singleton: true,
            eager: true,
          },
          '@progress/kendo-react-dropdowns': {
            singleton: true,
            eager: true,
          },
          '@progress/kendo-react-buttons': {
            singleton: true,
            eager: true,
          },
          '@progress/kendo-react-dialogs': {
            singleton: true,
            eager: true,
          },
          '@progress/kendo-react-dateinputs': {
            singleton: true,
            eager: true,
          },
          '@progress/kendo-react-intl': {
            singleton: true,
            eager: true,
          },
          '@progress/kendo-react-popup': {
            singleton: true,
            eager: true,
          },
          '@progress/kendo-react-animation': {
            singleton: true,
            eager: true,
          },
          '@progress/kendo-licensing': {
            singleton: true,
            eager: true,
          },
          '@progress/kendo-svg-icons': {
            singleton: true,
            eager: true,
          },
        },
    }),

    // Ignore problematic KendoReact layout and progressbar interface modules
    new webpack.IgnorePlugin({
      resourceRegExp: /^\.\/stepper\/interfaces\/|^\.\/appbar\/interfaces\/|^\.\/tilelayout\/interfaces\/|^\.\/bottomnavigation\/|^\.\/stacklayout\/|^\.\/gridlayout\/interfaces\/|^\.\/actionsheet\/interfaces\/|^\.\/chunkprogressbar\/interfaces\/|^\.\/progressbar\/interfaces\/|interfaces\/main$|models\/events$/,
      contextRegExp: /@progress\/kendo-react-layout|@progress\/kendo-react-progressbars|@progress\/kendo-react-dateinputs/
    }),

    // Additional ignore plugin for specific missing interface files
    new webpack.IgnorePlugin({
      resourceRegExp: /StepProps|StepChangeEvent|StepFocusEvent|AppBarProps|AppBarSectionProps|AppBarSpacerProps|BottomNavigationProps|BottomNavigationItemProps|StackLayoutProps|GridLayoutProps|GridLayoutItemProps|GridLayoutRowProps|GridLayoutColumnProps|ActionSheetItemProps|ChunkProgressBarProps|ProgressBarProps|ProgressBarAnimation|LabelProps|BottomNavigationProps|BottomNavigationItemProps/,
      contextRegExp: /@progress\/kendo-react-layout|@progress\/kendo-react-progressbars|@progress\/kendo-react-dateinputs/
    }),



    new HtmlWebpackPlugin({
      template: './index.html',
      title: 'SMSWS Micro Frontend',
    }),

    // Define environment variables
    new webpack.DefinePlugin({
      'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || 'development'),
      'process.env.REACT_APP_MOCK': JSON.stringify(process.env.REACT_APP_MOCK || 'false'),
      'process.env.REACT_APP_PROXY_URL': JSON.stringify(process.env.REACT_APP_PROXY_URL || 'http://localhost:8080'),
    }),
  ],

  performance: {
    // Disable performance warnings for micro frontend builds
    hints: false,
  },

  devServer: {
    port: 3001,
    hot: true,
    historyApiFallback: true,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, PATCH, OPTIONS',
      'Access-Control-Allow-Headers': 'X-Requested-With, content-type, Authorization',
    },
  },
};