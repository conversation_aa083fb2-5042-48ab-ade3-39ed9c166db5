import { test, expect, Page } from '@playwright/test';
import { navigateWithRetry } from './utils/test-helpers';

test.describe('Simple Tests', () => {
  test('should navigate to the Applications page', async ({ page }) => {
    // Navigate to the applications page
    await navigateWithRetry(page, '/');

    // Wait for the page to load
    await page.waitForSelector('#root', { timeout: 30000 });

    // Verify the page title
    const title = await page.locator('h1').first();
    const titleText = await title.textContent();
    console.log(`Page title: ${titleText}`);

    // Take a screenshot
    await page.screenshot({ path: 'screenshots/applications-page.png' });
  });

  test('should click the Name filter dropdown', async ({ page }) => {
    // Navigate to the applications page
    await navigateWithRetry(page, '/');

    // Wait for the page to load
    await page.waitForSelector('#root', { timeout: 30000 });

    // Find and click the name filter dropdown using role-based selector
    const nameCombobox = page.getByRole('combobox', { name: 'Name' });
    await expect(nameCombobox).toBeVisible({ timeout: 5000 });
    await nameCombobox.click();

    // Wait for the dropdown to appear
    await page.waitForTimeout(500);

    // Take a screenshot
    await page.screenshot({ path: 'screenshots/name-filter-dropdown.png' });

    // Verify that the dropdown options are visible
    const options = await page.getByRole('option').all();
    console.log(`Found ${options.length} filter options`);
    expect(options.length).toBeGreaterThan(0);
  });

  test('should click the Add button and check Network Address Type', async ({ page }) => {
    // Navigate to the applications page
    await navigateWithRetry(page, '/');

    // Wait for the page to load
    await page.waitForSelector('#root', { timeout: 30000 });

    // Click the Add button
    const addButton = page.getByRole('button', { name: 'Add' });
    await expect(addButton).toBeVisible({ timeout: 5000 });
    await addButton.click();

    // Wait for the form to load
    await page.waitForTimeout(1000);

    // Take a screenshot
    await page.screenshot({ path: 'screenshots/add-form.png' });

    // Find and click the Network Address Type dropdown
    const networkAddressTypeCombobox = page.getByRole('combobox', { name: 'Network Address Type' });
    await expect(networkAddressTypeCombobox).toBeVisible({ timeout: 5000 });
    await networkAddressTypeCombobox.click();

    // Wait for the dropdown to appear
    await page.waitForTimeout(500);

    // Take a screenshot
    await page.screenshot({ path: 'screenshots/network-address-type-dropdown.png' });

    // Verify that the dropdown options are visible
    const options = await page.getByRole('option').all();
    console.log(`Found ${options.length} network address type options`);
    expect(options.length).toBeGreaterThan(0);
  });
});
