import { test, expect } from '@playwright/test';
import { waitForPageLoad } from './utils/test-helpers';

test('basic test', async ({ page, browserName }) => {
  // Navigate to the home page with retry logic
  let retries = 3;
  let navigationSuccessful = false;

  while (retries > 0 && !navigationSuccessful) {
    try {
      await page.goto('/', { timeout: 30000 });
      await waitForPageLoad(page);
      navigationSuccessful = true;
    } catch (error) {
      console.log(`Navigation failed, retries left: ${retries-1}. Error: ${error.message}`);
      retries--;
      if (retries === 0) {
        throw error; // If all retries fail, throw the error
      }
      // Wait a bit before retrying
      await page.waitForTimeout(1000);
    }
  }

  // Check that the page title contains SMSWS
  const title = await page.title();
  expect(title, 'Page title should contain SMSWS').toContain('SMSWS');

  // Check that the root element exists
  const rootElement = await page.locator('#root');
  await expect(rootElement, 'Root element should be visible').toBeVisible();

  // Take a screenshot for verification
  await page.screenshot({ path: `screenshots/homepage-${browserName}.png` });

  console.log('Basic test completed successfully');
});
