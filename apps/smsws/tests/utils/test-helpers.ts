import { Page, expect } from '@playwright/test';

/**
 * Helper function to navigate to a page with retry logic for Firefox
 */
export async function navigateWithRetry(page: Page, url: string, maxRetries = 3): Promise<void> {
  let lastError;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`Navigation attempt ${attempt} to ${url}`);
      await page.goto(url, { timeout: 30000 });
      console.log(`Successfully navigated to ${url} on attempt ${attempt}`);
      return;
    } catch (error) {
      lastError = error;
      console.log(`Navigation attempt ${attempt} failed: ${error}`);

      if (attempt < maxRetries) {
        console.log(`Waiting 2 seconds before retry...`);
        try {
          await page.waitForTimeout(2000);
        } catch (timeoutError) {
          console.log(`Page closed during retry wait, aborting navigation attempts`);
          throw lastError;
        }
      }
    }
  }

  throw lastError;
}

/**
 * Helper function to wait for the page to load completely
 * with increased timeouts and better error handling
 */
export async function waitForPageLoad(page: Page): Promise<void> {
  try {
    // Wait for the page to load with a longer timeout
    await page.waitForLoadState('domcontentloaded', { timeout: 30000 });

    // Try to wait for network idle, but don't fail the test if it times out
    try {
      await page.waitForLoadState('networkidle', { timeout: 15000 });
    } catch (error) {
      console.log('Network did not reach idle state, but continuing test');
    }

    // Wait for the root element to be visible
    await page.locator('#root').waitFor({ state: 'visible', timeout: 30000 });

    // Wait for the main content to load - look for common page elements
    try {
      // Wait for either a table, form, or main content area to be visible
      await page.waitForSelector('table, form, .content-card, .wrapper, main, [role="main"]', { timeout: 15000 });
    } catch (error) {
      console.log('Main content area not found, but continuing test');
    }

    // Additional wait for any loading spinners to disappear
    try {
      await page.waitForSelector('.loading, .spinner, [data-testid="loading"]', { state: 'hidden', timeout: 5000 });
    } catch (error) {
      // No loading spinner found, which is fine
    }

    // Give the page a moment to settle
    await page.waitForTimeout(1000);
  } catch (error) {
    console.error('Error waiting for page to load:', error);
    // Take a screenshot of the current state for debugging
    await page.screenshot({ path: `screenshots/page-load-error-${Date.now()}.png` });
    throw error;
  }
}

/**
 * Helper function to verify that a table has loaded with data
 * with better error handling and more flexible selectors
 */
export async function verifyTableLoaded(page: Page, selector = '.MuiTable-root, table, [role="grid"], .k-grid'): Promise<number> {
  try {
    // Wait for the table/grid to be visible
    const table = page.locator(selector).first();
    await expect(table).toBeVisible({ timeout: 15000 });

    // Get the rows - try different selectors for different table types
    let rows;
    if (selector.includes('grid') || selector.includes('[role="grid"]')) {
      // For grids, look for rows in rowgroup
      rows = await page.locator(`${selector} [role="rowgroup"] [role="row"]`).all();
    } else {
      // For traditional tables
      rows = await page.locator(`${selector} tbody tr`).all();
    }

    const count = rows.length;

    console.log(`Found ${count} rows in table/grid`);
    return count;
  } catch (error) {
    console.error('Error verifying table loaded:', error);
    // Take a screenshot of the current state for debugging
    await page.screenshot({ path: `screenshots/table-load-error-${Date.now()}.png` });
    throw error;
  }
}

/**
 * Helper function to wait for the applications page to be fully loaded
 * including the Add button being visible
 */
export async function waitForApplicationsPageLoad(page: Page): Promise<void> {
  try {
    // First wait for the general page load
    await waitForPageLoad(page);

    // Wait for the applications table/grid to be visible
    await page.waitForSelector('table, .MuiTable-root, [role="grid"], .k-grid', { timeout: 15000 });

    // Wait for the Add button to be visible - try multiple selectors
    const addButtonSelectors = [
      'button:has-text("Add")',
      'button:has-text("Adicionar")',
      '[role="button"]:has-text("Add")',
      '[role="button"]:has-text("Adicionar")',
      'button[aria-label="Add"]',
      'button[aria-label="Adicionar"]'
    ];

    let addButtonFound = false;
    for (const selector of addButtonSelectors) {
      try {
        await page.waitForSelector(selector, { timeout: 5000 });
        console.log(`Found Add button with selector: ${selector}`);
        addButtonFound = true;
        break;
      } catch (error) {
        // Try next selector
      }
    }

    if (!addButtonFound) {
      console.log('Add button not found with any selector, but continuing test');
    }

    // Additional wait to ensure everything is settled
    await page.waitForTimeout(1000);
  } catch (error) {
    console.error('Error waiting for applications page to load:', error);
    await takeScreenshot(page, 'applications-page-load-error');
    throw error;
  }
}

/**
 * Helper function to take a screenshot with a descriptive name
 */
export async function takeScreenshot(page: Page, name: string): Promise<void> {
  try {
    // Check if page is still available before taking screenshot
    if (page.isClosed()) {
      console.log('Page is closed, skipping screenshot');
      return;
    }

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const path = `screenshots/${name}-${timestamp}.png`;
    await page.screenshot({ path, fullPage: true });
    console.log(`Screenshot saved to ${path}`);
  } catch (error) {
    console.error('Error taking screenshot:', error);
  }
}

/**
 * Helper function to log detailed information about the current state
 */
export async function logPageState(page: Page, context: string): Promise<void> {
  console.log(`--- Page State: ${context} ---`);
  console.log(`URL: ${page.url()}`);
  console.log(`Title: ${await page.title()}`);

  // Add console listener (Playwright will handle duplicates)
  page.on('console', msg => {
    console.log(`Browser console [${msg.type()}]: ${msg.text()}`);
  });

  // Log any errors
  page.on('pageerror', error => {
    console.error('Page error:', error);
  });
}

/**
 * Helper function to wait for a toast notification to appear and capture its text
 */
export async function waitForToast(page: Page): Promise<string | null> {
  try {
    const toast = page.locator('.Toastify__toast-body, .toast-message').first();
    await expect(toast).toBeVisible({ timeout: 5000 });
    const toastText = await toast.textContent();
    console.log(`Toast message: ${toastText}`);
    return toastText;
  } catch (error) {
    console.log('No toast notification appeared');
    return null;
  }
}

/**
 * Helper function to fill a form field with a value
 */
export async function fillFormField(page: Page, selector: string, value: string): Promise<void> {
  try {
    const field = page.locator(selector);
    await expect(field, `Field ${selector} should be visible`).toBeVisible({ timeout: 5000 });
    await field.fill(value);
  } catch (error) {
    console.error(`Error filling form field ${selector}:`, error);
    await takeScreenshot(page, `fill-field-error-${selector.replace(/[^a-zA-Z0-9]/g, '-')}`);
    throw error;
  }
}

/**
 * Helper function to select an option from a dropdown
 */
export async function selectDropdownOption(page: Page, selector: string, option: string | number): Promise<void> {
  try {
    // First try to find the dropdown by role and name
    const selectorName = selector.match(/name="([^"]+)"/)?.[1] || selector;

    // Try multiple selectors to find the dropdown
    const selectors = [
      // Try role-based selector first
      page.getByRole('combobox', { name: new RegExp(selectorName, 'i') }),
      // Try by name attribute
      page.locator(`[name="${selector}"]`),
      // Try by data-field attribute
      page.locator(`[data-field="${selector}"]`),
      // Try by id attribute
      page.locator(`#${selector}`),
      // Try by class that contains the field name
      page.locator(`.${selector}`),
      // Try direct selector
      page.locator(selector)
    ];

    let dropdownFound = false;
    let dropdownElement;

    // Try each selector until one works
    for (const locator of selectors) {
      if (await locator.isVisible({ timeout: 1000 }).catch(() => false)) {
        dropdownElement = locator;
        dropdownFound = true;
        break;
      }
    }

    if (!dropdownFound) {
      console.log(`Could not find dropdown ${selector} using any selector, skipping`);
      return;
    }

    // Click the dropdown to open it with better error handling
    try {
      await dropdownElement!.click({ timeout: 5000 });
    } catch (clickError) {
      console.log(`Error clicking dropdown ${selector}, trying force click:`, clickError);
      // Try force click as fallback
      await dropdownElement!.click({ force: true, timeout: 5000 });
    }

    // Wait for the dropdown to appear
    await page.waitForTimeout(500);

    try {
      if (typeof option === 'string') {
        // Try multiple approaches to find the option by text
        const optionSelectors = [
          page.getByRole('option', { name: new RegExp(option, 'i') }),
          page.locator('li').filter({ hasText: option }),
          page.locator('.MuiMenuItem-root').filter({ hasText: option }),
          page.locator('.k-list-item').filter({ hasText: option }),
          page.locator('option').filter({ hasText: option })
        ];

        let optionFound = false;
        for (const optionLocator of optionSelectors) {
          if (await optionLocator.isVisible({ timeout: 1000 }).catch(() => false)) {
            await optionLocator.click();
            optionFound = true;
            break;
          }
        }

        if (!optionFound) {
          console.log(`Could not find option ${option} in dropdown ${selector}`);
        }
      } else {
        // Try multiple selectors for dropdown options by index
        const optionSelectors = [
          page.getByRole('option'),
          page.locator('li[role="option"]'),
          page.locator('.MuiMenuItem-root'),
          page.locator('.k-list-item'),
          page.locator('option')
        ];

        let optionFound = false;
        for (const optionLocator of optionSelectors) {
          const count = await optionLocator.count();
          if (count > 0) {
            if (count > option) {
              await optionLocator.nth(option).click();
            } else if (count > 0) {
              // If the requested index is out of bounds, select the first option
              await optionLocator.first().click();
            }
            optionFound = true;
            break;
          }
        }

        if (!optionFound) {
          console.log(`No options found for dropdown ${selector}`);
        }
      }
    } catch (error) {
      console.log(`Error selecting option in dropdown ${selector}, trying selectOption method:`, error);

      // Try the selectOption method as a last resort
      try {
        if (typeof option === 'string') {
          await dropdownElement!.selectOption(option);
        } else {
          await dropdownElement!.selectOption({ index: option });
        }
      } catch (selectError) {
        console.log(`Error using selectOption method:`, selectError);
      }
    }
  } catch (error) {
    console.log(`Error selecting option in dropdown ${selector}:`, error);
    await takeScreenshot(page, `select-dropdown-error-${selector.replace(/[^a-zA-Z0-9]/g, '-')}`);
  }
}

/**
 * Helper function to click a button or link
 */
export async function clickElement(page: Page, selector: string): Promise<void> {
  try {
    // Extract button/link text from selector if it contains it
    const buttonTextMatch = selector.match(/:has-text\("([^"]+)"\)/);
    const buttonText = buttonTextMatch ? buttonTextMatch[1] : null;

    try {
      // Try multiple approaches to find and click the element
      if (buttonText) {
        // Check if this is a common button that might be internationalized
        const commonButtons: Record<string, string[]> = {
          'Add': ['Add', 'Añadir', 'Agregar', 'Ajouter', 'Hinzufügen', 'Adicionar', 'Aggiungi', 'Toevoegen', 'Lägg till', 'Tilføj', 'Lisää', '追加', '添加', '추가', 'Добавить', 'Додати', 'Dodaj', 'Adăuga', 'Ekle', 'Προσθήκη', 'إضافة', 'הוסף'],
          'Submit': ['Submit', 'Enviar', 'Soumettre', 'Absenden', 'Invia', 'Verzenden', 'Skicka', 'Send', 'Lähetä', '送信', '提交', '제출', 'Отправить', 'Надіслати', 'Pošalji', 'Trimite', 'Gönder', 'Υποβολή', 'إرسال', 'שלח'],
          'Cancel': ['Cancel', 'Cancelar', 'Annuler', 'Abbrechen', 'Annulla', 'Annuleren', 'Avbryt', 'Annuller', 'Peruuta', 'キャンセル', '取消', '취소', 'Отмена', 'Скасувати', 'Odustani', 'Anulare', 'İptal', 'Ακύρωση', 'إلغاء', 'בטל'],
          'Delete': ['Delete', 'Eliminar', 'Supprimer', 'Löschen', 'Elimina', 'Verwijderen', 'Ta bort', 'Slet', 'Poista', '削除', '删除', '삭제', 'Удалить', 'Видалити', 'Izbriši', 'Șterge', 'Sil', 'Διαγραφή', 'حذف', 'מחק'],
          'Save': ['Save', 'Guardar', 'Sauvegarder', 'Speichern', 'Salva', 'Opslaan', 'Spara', 'Gem', 'Tallenna', '保存', '保存', '저장', 'Сохранить', 'Зберегти', 'Spremi', 'Salvează', 'Kaydet', 'Αποθήκευση', 'حفظ', 'שמור'],
          'Edit': ['Edit', 'Editar', 'Modifier', 'Bearbeiten', 'Modifica', 'Bewerken', 'Redigera', 'Rediger', 'Muokkaa', '編集', '编辑', '편집', 'Редактировать', 'Редагувати', 'Uredi', 'Editare', 'Düzenle', 'Επεξεργασία', 'تحرير', 'ערוך'],
          'Close': ['Close', 'Cerrar', 'Fermer', 'Schließen', 'Chiudi', 'Sluiten', 'Stäng', 'Luk', 'Sulje', '閉じる', '关闭', '닫기', 'Закрыть', 'Закрити', 'Zatvori', 'Închide', 'Kapat', 'Κλείσιμο', 'إغلاق', 'סגור'],
          'Search': ['Search', 'Buscar', 'Rechercher', 'Suchen', 'Cerca', 'Zoeken', 'Sök', 'Søg', 'Haku', '検索', '搜索', '검색', 'Поиск', 'Пошук', 'Pretraži', 'Caută', 'Ara', 'Αναζήτηση', 'بحث', 'חיפוש'],
          'Apply': ['Apply', 'Aplicar', 'Appliquer', 'Anwenden', 'Applica', 'Toepassen', 'Tillämpa', 'Anvend', 'Käytä', '適用', '应用', '적용', 'Применить', 'Застосувати', 'Primijeni', 'Aplică', 'Uygula', 'Εφαρμογή', 'تطبيق', 'החל'],
          'OK': ['OK', 'Aceptar', 'D\'accord', 'OK', 'OK', 'OK', 'OK', 'OK', 'OK', 'OK', '确定', '확인', 'ОК', 'ОК', 'U redu', 'OK', 'Tamam', 'Εντάξει', 'موافق', 'אישור']
        };

        // Check if the button text is one of our common buttons
        let buttonVariations: string[] = [];
        for (const [key, translations] of Object.entries(commonButtons)) {
          if (buttonText.toLowerCase() === key.toLowerCase()) {
            buttonVariations = translations;
            break;
          }
        }

        // If no variations found, just use the original text
        if (buttonVariations.length === 0) {
          buttonVariations = [buttonText];
        }

        // Try each button variation with multiple selectors
        for (const btnText of buttonVariations) {
          // Try multiple selectors for each button text
          const selectors = [
            // Try role-based selector first
            page.getByRole('button', { name: btnText }),
            // Try text-based selector
            page.getByText(btnText, { exact: true }).filter({ hasText: btnText }),
            // Try CSS selector
            page.locator(`button:has-text("${btnText}")`),
            // Try more general selector
            page.locator('button').filter({ hasText: btnText })
          ];

          // Try each selector until one works
          for (const locator of selectors) {
            if (await locator.isVisible({ timeout: 500 }).catch(() => false)) {
              console.log(`Found button with text: "${btnText}"`);
              await locator.click();
              return;
            }
          }
        }

        // If we get here, none of the selectors worked, try icon buttons
        const iconSelectors = [
          `button[aria-label="${buttonText}"]`,
          `button[title="${buttonText}"]`,
          `button.${buttonText.toLowerCase()}-button`,
          `button svg[data-testid="${buttonText}Icon"]`
        ];

        for (const iconSelector of iconSelectors) {
          const iconButton = page.locator(iconSelector);
          if (await iconButton.isVisible({ timeout: 500 }).catch(() => false)) {
            console.log(`Found button with icon selector: "${iconSelector}"`);
            await iconButton.click();
            return;
          }
        }

        // If still not found, try one more time with the original text
        const button = page.getByRole('button', { name: buttonText });
        await expect(button, `Button "${buttonText}" should be visible`).toBeVisible({ timeout: 5000 });
        await button.click();
        return;
      } else if (selector.includes('button')) {
        // Try to find any button that matches part of the selector
        const buttonName = selector.match(/button\[name="([^"]+)"\]/)?.[1];
        if (buttonName) {
          const button = page.getByRole('button', { name: new RegExp(buttonName, 'i') });
          await expect(button, `Button ${buttonName} should be visible`).toBeVisible({ timeout: 5000 });
          await button.click();
          return;
        }
      }

      // Fall back to the original selector
      const element = page.locator(selector);
      await expect(element, `Element ${selector} should be visible`).toBeVisible({ timeout: 5000 });
      await element.click();
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error(`Error with primary click method: ${errorMessage}`);

      // Try a more generic approach as a fallback
      if (buttonText) {
        try {
          // Try to find by text content
          const elementByText = page.getByText(buttonText, { exact: true });
          await expect(elementByText, `Element with text "${buttonText}" should be visible`).toBeVisible({ timeout: 5000 });
          await elementByText.click();
          return;
        } catch (textError) {
          console.error(`Error finding element by text: ${textError}`);
        }
      }

      // If all else fails, throw the original error
      throw error;
    }
  } catch (error) {
    console.error(`Error clicking element ${selector}:`, error);
    await takeScreenshot(page, `click-element-error-${selector.replace(/[^a-zA-Z0-9]/g, '-')}`);
    throw error;
  }
}

/**
 * Helper function to check if an element exists on the page
 */
export async function elementExists(page: Page, selector: string): Promise<boolean> {
  const count = await page.locator(selector).count();
  return count > 0;
}

/**
 * Helper function to get validation error messages from a form
 */
export async function getValidationErrors(page: Page): Promise<string[]> {
  const errorElements = await page.locator('.Mui-error, .MuiFormHelperText-root').all();
  const errors: string[] = [];

  for (const element of errorElements) {
    const text = await element.textContent();
    if (text) {
      errors.push(text.trim());
    }
  }

  return errors;
}
