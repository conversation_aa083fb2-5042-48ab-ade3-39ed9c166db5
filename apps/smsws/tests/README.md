# E2E Testing with <PERSON>wright

This directory contains end-to-end tests for the SMSWS application using Playwright.

## Test Structure

- `tests/` - Main test directory
  - `applications.spec.ts` - Tests for the Applications page
  - `example.spec.ts` - Basic test example
  - `common/` - Tests for the common package
    - `services.spec.ts` - Tests for common services
  - `utils/` - Test utilities
    - `test-helpers.ts` - Helper functions for tests

## Running Tests

You can run the tests using the following npm scripts:

```bash
# Run all tests
pnpm test

# Run tests with UI mode (for debugging)
pnpm test:ui

# Run tests in debug mode
pnpm test:debug

# Show the HTML report after tests have run
pnpm test:report
```

## Test Configuration

The test configuration is defined in `playwright.config.ts` in the root of the SMSWS app. Key settings include:

- Tests run against `http://localhost:5173` (the Vite dev server)
- Tests are run in Chromium, Firefox, and WebKit
- Screenshots are taken on test failure
- A trace is recorded on first retry

## Writing New Tests

When writing new tests:

1. Create a new `.spec.ts` file in the appropriate directory
2. Use the helper functions from `utils/test-helpers.ts` for common operations
3. Add descriptive error messages to assertions
4. Take screenshots at key points for debugging

## Test Feedback

Tests are configured to provide detailed feedback when they fail:

- Screenshots are automatically taken on failure
- Console logs are captured and displayed
- Detailed error messages are shown in the test report
- Traces can be viewed for debugging complex issues

## Common Issues

- If tests fail with timeout errors, check that the application is running
- If selectors don't match, check if the UI structure has changed
- If network requests fail, check that the backend services are running
