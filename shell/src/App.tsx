import React, { Suspense } from 'react';
import { BrowserRouter as Router, Routes, Route, Link, useLocation } from 'react-router-dom';

// Lazy load the SMSWS micro frontend components
const SMSWSApplications = React.lazy(() => import('smsws/MicroFrontend').then(module => ({
  default: module.ApplicationsPage
})));

const SMSWSAppContent = React.lazy(() => import('smsws/AppContent'));

const Navigation = () => {
  const location = useLocation();

  const isActive = (path: string) => location.pathname === path;

  return (
    <nav className="shell-nav">
      <ul>
        <li>
          <Link to="/" className={isActive('/') ? 'active' : ''}>
            Home
          </Link>
        </li>
        <li>
          <Link to="/test" className={isActive('/test') ? 'active' : ''}>
            Test Page
          </Link>
        </li>
        <li>
          <Link to="/smsws-applications" className={isActive('/smsws-applications') ? 'active' : ''}>
            SMSWS Applications
          </Link>
        </li>
        <li>
          <Link to="/smsws-full" className={isActive('/smsws-full') ? 'active' : ''}>
            SMSWS Full App
          </Link>
        </li>
      </ul>
    </nav>
  );
};

const HomePage = () => {
  const [testResult, setTestResult] = React.useState<string>('');

  const testMicroFrontend = async () => {
    setTestResult('🔄 Testing micro frontend integration...');
    try {
      // Test if we can import the SMSWS module
      const module = await import('smsws/MicroFrontend');
      if (module.ApplicationsPage) {
        setTestResult('✅ Micro frontend integration working! ApplicationsPage component loaded successfully.');
      } else {
        setTestResult('❌ ApplicationsPage component not found in the module.');
      }
    } catch (error) {
      setTestResult(`❌ Failed to load micro frontend: ${error.message}`);
    }
  };

  return (
    <div>
      <h1>Welcome to the Shell Application</h1>
      <p>This is a simple shell application that will integrate SMSWS micro frontend components.</p>

      <h2>Current Status:</h2>
      <ul>
        <li>✅ Shell application setup complete</li>
        <li>✅ Webpack Module Federation configured</li>
        <li>✅ SMSWS server running on port 3001</li>
        <li>✅ Shell server running on port 3002</li>
      </ul>

      <h2>Micro Frontend Test:</h2>
      <button
        onClick={testMicroFrontend}
        style={{
          padding: '10px 20px',
          backgroundColor: '#007bff',
          color: 'white',
          border: 'none',
          borderRadius: '4px',
          cursor: 'pointer',
          marginBottom: '10px'
        }}
      >
        Test Micro Frontend Integration
      </button>

      {testResult && (
        <div style={{
          padding: '10px',
          backgroundColor: testResult.includes('✅') ? '#d4edda' : testResult.includes('❌') ? '#f8d7da' : '#d1ecf1',
          color: testResult.includes('✅') ? '#155724' : testResult.includes('❌') ? '#721c24' : '#0c5460',
          border: `1px solid ${testResult.includes('✅') ? '#c3e6cb' : testResult.includes('❌') ? '#f5c6cb' : '#bee5eb'}`,
          borderRadius: '4px',
          marginTop: '10px'
        }}>
          {testResult}
        </div>
      )}

      <h2>Navigation Links:</h2>
      <p>Use the navigation above to test the micro frontend pages:</p>
      <ul>
        <li><strong>SMSWS Applications</strong> - Loads the Applications page from SMSWS micro frontend</li>
        <li><strong>SMSWS Full App</strong> - Loads the complete SMSWS application</li>
      </ul>
    </div>
  );
};

const TestPage = () => (
  <div>
    <h1>Test Page</h1>
    <p>This is a test page to verify the shell application is working correctly.</p>
  </div>
);

function App() {
  return (
    <Router>
      <div className="shell-container">
        <header className="shell-header">
          <h1>Shell Application - SMSWS Integration</h1>
        </header>

        <Navigation />

        <main className="shell-content">
          <Routes>
            <Route path="/" element={<HomePage />} />
            <Route path="/test" element={<TestPage />} />
            <Route
              path="/smsws-applications"
              element={
                <Suspense fallback={<div>Loading SMSWS Applications...</div>}>
                  <SMSWSApplications />
                </Suspense>
              }
            />
            <Route
              path="/smsws-full/*"
              element={
                <Suspense fallback={<div>Loading SMSWS Full App...</div>}>
                  <SMSWSAppContent />
                </Suspense>
              }
            />
          </Routes>
        </main>
      </div>
    </Router>
  );
}

export default App;
