{"name": "shell", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "webpack serve --mode development --config webpack.config.cjs", "build": "webpack --mode production --config webpack.config.cjs", "preview": "webpack serve --mode production --config webpack.config.cjs"}, "dependencies": {"@emotion/react": "^11.0.0", "@emotion/styled": "^11.0.0", "@mui/icons-material": "^5.0.0", "@mui/material": "^5.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^7.5.0"}, "devDependencies": {"@module-federation/enhanced": "^0.14.3", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "css-loader": "^7.1.2", "html-webpack-plugin": "^5.6.3", "style-loader": "^4.0.0", "ts-loader": "^9.5.2", "typescript": "^5.3.3", "webpack": "^5.99.9", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.2.1"}}