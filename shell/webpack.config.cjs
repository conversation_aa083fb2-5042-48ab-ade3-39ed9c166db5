const { ModuleFederationPlugin } = require('@module-federation/enhanced');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const path = require('path');

module.exports = (env, argv) => {
  const isProduction = argv.mode === 'production';

  return {
    mode: isProduction ? 'production' : 'development',
    entry: './src/index.tsx',
    devtool: isProduction ? 'source-map' : 'eval-source-map',

    output: {
      publicPath: 'auto'
    },

    resolve: {
      extensions: ['.tsx', '.ts', '.jsx', '.js'],
    },

    module: {
      rules: [
        {
          test: /\.tsx?$/,
          use: {
            loader: 'ts-loader',
            options: {
              transpileOnly: true
            }
          },
          exclude: /node_modules/,
        },
        {
          test: /\.css$/,
          use: ['style-loader', 'css-loader'],
        },
        {
          test: /\.(png|jpe?g|gif|svg)$/,
          type: 'asset/resource',
        },
      ],
    },

    plugins: [
      new ModuleFederationPlugin({
        name: 'shell',
        remotes: {
          smsws: 'smsws@http://localhost:3001/remoteEntry.js',
        },
        shared: {
          react: {
            singleton: true,
            requiredVersion: '^18.2.0',
            eager: true,
          },
          'react-dom': {
            singleton: true,
            requiredVersion: '^18.2.0',
            eager: true,
          },
          'react-router-dom': {
            singleton: true,
            requiredVersion: '^7.5.0',
            eager: true,
          },
          '@mui/material': {
            singleton: true,
            requiredVersion: '7.0.2',
            version: '7.0.2',
            eager: true,
          },
          '@mui/icons-material': {
            singleton: true,
            requiredVersion: '^7.0.0',
            eager: true,
          },
          '@mui/system': {
            singleton: true,
            requiredVersion: '^7.0.2',
            eager: true,
          },
          '@mui/styled-engine': {
            singleton: true,
            requiredVersion: '^7.0.1',
            eager: true,
          },
          '@mui/utils': {
            singleton: true,
            requiredVersion: '^7.1.1',
            eager: true,
          },
          '@emotion/react': {
            singleton: true,
            requiredVersion: '^11.14.0',
            eager: true,
          },
          '@emotion/styled': {
            singleton: true,
            requiredVersion: '^11.14.0',
            eager: true,
          },
          '@emotion/cache': {
            singleton: true,
            requiredVersion: '^11.14.0',
            eager: true,
          },
          'prop-types': {
            singleton: true,
            requiredVersion: '^15.8.0',
            eager: true,
          },
          // Kendo React packages
          '@progress/kendo-react-common': {
            singleton: true,
            eager: true,
          },
          '@progress/kendo-react-grid': {
            singleton: true,
            eager: true,
          },
          '@progress/kendo-react-data-tools': {
            singleton: true,
            eager: true,
          },
          '@progress/kendo-react-inputs': {
            singleton: true,
            eager: true,
          },
          '@progress/kendo-react-dropdowns': {
            singleton: true,
            eager: true,
          },
          '@progress/kendo-react-buttons': {
            singleton: true,
            eager: true,
          },
          '@progress/kendo-react-dialogs': {
            singleton: true,
            eager: true,
          },
          '@progress/kendo-react-dateinputs': {
            singleton: true,
            eager: true,
          },
          '@progress/kendo-react-intl': {
            singleton: true,
            eager: true,
          },
          '@progress/kendo-react-popup': {
            singleton: true,
            eager: true,
          },
          '@progress/kendo-react-animation': {
            singleton: true,
            eager: true,
          },
          '@progress/kendo-licensing': {
            singleton: true,
            eager: true,
          },
          '@progress/kendo-svg-icons': {
            singleton: true,
            eager: true,
          },
        },
      }),

      new HtmlWebpackPlugin({
        template: './public/index.html',
        title: 'Shell Application',
      }),
    ],

    devServer: {
      port: 3002,
      hot: true,
      historyApiFallback: true,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, PATCH, OPTIONS',
        'Access-Control-Allow-Headers': 'X-Requested-With, content-type, Authorization',
      },
    },
  };
};
