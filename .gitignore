node_modules
**/node_modules

dist
**/dist

.idea
./.idea
.idea/*

# Added by <PERSON> Task Master
# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dev-debug.log
# Dependency directories
node_modules/
# Environment variables
.env
# Editor directories and files
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# OS specific
.DS_Store
# Task files
tasks.json
tasks/

.github
playwright-report
screenshots
test-results

.claude
.cursor
CLAUDE.md
**/.claude
**/.cursor
**/CLAUDE.md

# Ignore all .env files
.env

.env.*
.env.local
.env.development.local
.env.test.local
.env.production.local

.pnpm-store
.pnpm-debug.log
.pnpmfile.js
.pnpmfile.cjs
.pnpmfile.mjs
.pnpmfile.json
.pnpmfile.yaml
.pnpmfile.yml
.pnpmfile.toml
.pnpmfile.ts
.pnpmfile.cts
.pnpmfile.mts
.pnpmfile.js
.pnpmfile.cjs
.pnpmfile.mjs
.pnpmfile.json
.pnpmfile.yaml
.pnpm